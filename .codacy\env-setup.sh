#!/bin/bash
set -e

echo "=== BanditGUI Development Environment Setup ==="

# Update system packages
sudo apt-get update

# Install Python 3 and pip if not already installed
sudo apt-get install -y python3 python3-pip python3-venv

# Install Node.js and npm
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify installations
echo "Python version: $(python3 --version)"
echo "pip version: $(pip3 --version)"
echo "Node.js version: $(node --version)"
echo "npm version: $(npm --version)"

# Create Python virtual environment
echo "Creating Python virtual environment..."
python3 -m venv venv

# Activate virtual environment and add to profile
echo "Activating virtual environment..."
source venv/bin/activate

# Add virtual environment activation to profile
echo "# BanditGUI virtual environment" >> $HOME/.profile
echo "source $(pwd)/venv/bin/activate" >> $HOME/.profile

# Add current directory to <PERSON>YTHON<PERSON>TH in profile so banditgui module can be imported
echo "export PYTHONPATH=\$PYTHONPATH:$(pwd)" >> $HOME/.profile
export PYTHONPATH=$PYTHONPATH:$(pwd)

# Upgrade pip in virtual environment
pip install --upgrade pip

# Install Python dependencies
echo "Installing Python dependencies..."
pip install -r requirements.txt

# Install Node.js dependencies
echo "Installing Node.js dependencies..."
npm install

# Build frontend assets
echo "Building frontend assets..."
npm run build

# Create necessary directories and files for tests
echo "Setting up test environment..."

# Ensure config directory exists with required files
mkdir -p banditgui/config
if [ ! -f banditgui/config/llm_model.json ]; then
    echo '{"openai": ["gpt-4o"], "ollama": ["qwen2.5-1.5b"], "openrouter": ["nous-hermes-2"]}' > banditgui/config/llm_model.json
fi

# Create .env file if it doesn't exist (for tests that might need it)
if [ ! -f .env ]; then
    touch .env
fi

echo "=== Setup completed successfully ==="
echo "Virtual environment activated and added to ~/.profile"
echo "PYTHONPATH configured to include project directory"
echo "All dependencies installed and frontend assets built"