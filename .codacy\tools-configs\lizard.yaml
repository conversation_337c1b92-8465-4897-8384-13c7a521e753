patterns:
    Lizard_ccn-critical:
        category: Complexity
        description: This rule checks the cyclomatic complexity of functions or logic blocks and raises a critical issue if the complexity exceeds the default threshold of 12.
        explanation: |-
            # Critical Cyclomatic Complexity control

            Check the Cyclomatic Complexity value of a function or logic block. If the threshold is not met, raise a Critical issue. The default threshold is 10.
        id: Lizard_ccn-critical
        level: Error
        severityLevel: Error
        threshold: 12
        timeToFix: 15
        title: Enforce Critical Cyclomatic Complexity Threshold
    Lizard_ccn-medium:
        category: Complexity
        description: Checks if the cyclomatic complexity of a function or logic block exceeds the medium threshold (default is 8).
        explanation: |-
            # Medium Cyclomatic Complexity control

            Check the Cyclomatic Complexity value of a function or logic block. If the threshold is not met, raise a Medium issue. The default threshold is 7.
        id: Lizard_ccn-medium
        level: Warning
        severityLevel: Warning
        threshold: 8
        timeToFix: 10
        title: Enforce Medium Cyclomatic Complexity Threshold
    Lizard_ccn-minor:
        category: Complexity
        description: Checks that the cyclomatic complexity of functions or logic blocks does not exceed a minor threshold, defaulting to 5.
        explanation: |-
            # Minor Cyclomatic Complexity control

            Check the Cyclomatic Complexity value of a function or logic block. If the threshold is not met, raise a Minor issue. The default threshold is 4.
        id: Lizard_ccn-minor
        level: Info
        severityLevel: Info
        threshold: 5
        timeToFix: 5
        title: Enforce Minor Cyclomatic Complexity Threshold
    Lizard_file-nloc-critical:
        category: Complexity
        description: Checks the number of lines of code (excluding comments) in a file and raises a critical issue if it exceeds the threshold (default 1000 lines).
        explanation: |4-
            At Codacy we strive to provide great descriptions for our patterns.
            With good explanations developers can better understand issues and even learn how to fix them.

            For this tool we are not yet meeting this standard but you can help us improve the docs.
            To know more, take a look at our [tool documentation guide](https://docs.codacy.com/related-tools/tool-developer-guide/#documentation).

            You can also visit the tool's website to find useful tips about the patterns.
        id: Lizard_file-nloc-critical
        level: Error
        severityLevel: Error
        threshold: 1000
        timeToFix: 20
        title: Enforce Maximum Number of Lines of Code per File
    Lizard_file-nloc-medium:
        category: Complexity
        description: This rule checks if the number of lines of code (excluding comments) in a file exceeds a medium threshold, typically 500 lines.
        explanation: |4-
            At Codacy we strive to provide great descriptions for our patterns.
            With good explanations developers can better understand issues and even learn how to fix them.

            For this tool we are not yet meeting this standard but you can help us improve the docs.
            To know more, take a look at our [tool documentation guide](https://docs.codacy.com/related-tools/tool-developer-guide/#documentation).

            You can also visit the tool's website to find useful tips about the patterns.
        id: Lizard_file-nloc-medium
        level: Warning
        severityLevel: Warning
        threshold: 500
        timeToFix: 10
        title: Enforce Medium File Length Limit Based on Number of Lines of Code
    Lizard_file-nloc-minor:
        category: Complexity
        description: Checks that the number of lines of code (excluding comments) in a file meets a minimum threshold, defaulting to 300 lines.
        explanation: |4-
            At Codacy we strive to provide great descriptions for our patterns.
            With good explanations developers can better understand issues and even learn how to fix them.

            For this tool we are not yet meeting this standard but you can help us improve the docs.
            To know more, take a look at our [tool documentation guide](https://docs.codacy.com/related-tools/tool-developer-guide/#documentation).

            You can also visit the tool's website to find useful tips about the patterns.
        id: Lizard_file-nloc-minor
        level: Info
        severityLevel: Info
        threshold: 300
        timeToFix: 5
        title: Enforce Minimum Number of Lines of Code per File
    Lizard_nloc-critical:
        category: Complexity
        description: Checks if functions or logic blocks exceed a set maximum number of lines of code (excluding comments), defaulting to 100 lines.
        explanation: |-
            # Critical NLOC control - Number of Lines of Code (without comments)

            Check the number of lines of code (without comments) in a function or logic block. If the threshold is not met, raise a Critical issue. The default threshold is 100.
        id: Lizard_nloc-critical
        level: Error
        severityLevel: Error
        threshold: 100
        timeToFix: 15
        title: Enforce Maximum Number of Lines of Code in Functions
    Lizard_nloc-medium:
        category: Complexity
        description: Checks if the number of lines of code (excluding comments) in a function exceeds a medium threshold (default 50 lines).
        explanation: |-
            # Medium NLOC control - Number of Lines of Code (without comments)

            Check the number of lines of code (without comments) in a function. If the threshold is not met, raise a Medium issue. The default threshold is 50.
        id: Lizard_nloc-medium
        level: Warning
        severityLevel: Warning
        threshold: 50
        timeToFix: 10
        title: Enforce Medium Number of Lines of Code (NLOC) Limit
    Lizard_nloc-minor:
        category: Complexity
        description: Checks if functions have at least a minimum number of lines of code (excluding comments), raising a minor issue if they are too short.
        explanation: |-
            # Minor NLOC control - Number of Lines of Code (without comments)

            Check the number of lines of code (without comments) in a function. If the threshold is not met, raise a Minor issue. The default threshold is 20.
        id: Lizard_nloc-minor
        level: Info
        severityLevel: Info
        threshold: 20
        timeToFix: 5
        title: Enforce Minimum Number of Lines of Code in Functions
    Lizard_parameter-count-critical:
        category: Complexity
        description: This rule checks if a function has more parameters than a specified threshold and raises a critical issue if exceeded.
        explanation: |-
            # Critical Parameter count control

            Check the number of parameters sent to a function. If the threshold is not met, raise a Critical issue. The default threshold is 9.
        id: Lizard_parameter-count-critical
        level: Error
        severityLevel: Error
        threshold: 12
        timeToFix: 15
        title: Avoid Excessive Function Parameter Count
    Lizard_parameter-count-medium:
        category: Complexity
        description: This rule checks the number of parameters passed to a function and raises an issue if it exceeds a medium threshold, which by default is 8 parameters.
        explanation: |-
            # Medium Parameter count control

            Check the number of parameters sent to a function. If the threshold is not met, raise a Medium issue. The default threshold is 5.
        id: Lizard_parameter-count-medium
        level: Warning
        severityLevel: Warning
        threshold: 8
        timeToFix: 10
        title: Enforce Medium Parameter Count Limit
    Lizard_parameter-count-minor:
        category: Complexity
        description: Checks if functions have more parameters than a specified threshold, raising a minor issue if exceeded.
        explanation: |-
            # Minor Parameter count control

            Check the number of parameters sent to a function. If the threshold is not met, raise a Minor issue. The default threshold is 3.
        id: Lizard_parameter-count-minor
        level: Info
        severityLevel: Info
        threshold: 4
        timeToFix: 5
        title: Enforce Parameter Count Threshold in Functions
