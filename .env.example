# SSH Connection Settings (rarely need to change these for Bandit)
SSH_HOST="bandit.labs.overthewire.org"
SSH_PORT="2220"
SSH_USERNAME="bandit0" # Initial username, will change as you solve levels
SSH_PASSWORD="bandit0" # Initial password, will change as you solve levels

# Flask Application Settings
DEBUG="True" # Set to "False" in production
HOST="127.0.0.1"
PORT="5000"

# Logging Configuration
LOG_LEVEL="INFO" # Options: DEBUG, INFO, WARNING, ERROR, CRITICAL

# --- LLM Provider API Keys and Settings ---
# Fill in the API key for the provider you intend to use.
# You only need to set the key for your chosen provider.

# OpenAI API Key
OPENAI_API_KEY="YOUR_OPENAI_API_KEY_HERE"

# Google Gemini API Key
GEMINI_API_KEY="YOUR_GEMINI_API_KEY_HERE"

# OpenRouter API Key (for access to various models)
OPENROUTER_API_KEY="YOUR_OPENROUTER_API_KEY_HERE"

# Ollama Settings (if using a local Ollama instance)
OLLAMA_BASE_URL="http://localhost:11434" # Default, uncomment if different

# Preferred LLM Provider and Model for the "Ask a Pro" feature
# Supported providers by LiteLLM: "openai", "gemini", "openrouter", "ollama", etc.
# Ensure the model is compatible with the chosen provider and your API key access.
PREFERRED_LLM_PROVIDER="gemini"
# Examples: "gpt-3.5-turbo", "gpt-4", "gemini/gemini-pro", "google/gemini-pro" (for OpenRouter), "ollama/llama2"
PREFERRED_LLM_MODEL="gemini-2.5-flash-preview"

# --- Other Settings ---
# Example: FEATURE_FLAG_XYZ="True"
