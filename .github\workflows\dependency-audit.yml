name: Dependency Audit

on:
  push:
    paths:
      - 'requirements.txt'
  pull_request:
    paths:
      - 'requirements.txt'
  schedule:
    - cron: '0 0 * * 1' # every Monday

jobs:
  pip-audit:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.x'
      - name: Install pip-audit
        run: pip install pip-audit
      - name: Run pip-audit
        run: pip-audit
