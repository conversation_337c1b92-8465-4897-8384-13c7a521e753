# Change Log

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.5.1] - 2025-07-07

### Security

- Added comprehensive session management with secure cookie handling and defensive checks for missing/invalid sessions (returns 401 Unauthorized).
- Implemented custom CSRF protection decorator for all state-changing endpoints; now returns 400 Bad Request for missing or invalid CSRF tokens.
- Added rate limiting for API endpoints with clear 429 responses.
- Improved input validation and sanitization.
- Enhanced logging for security events.
- Added session timeout and refresh mechanisms.
- Implemented secure password handling and storage.
- Improved error handling for all security-related failures, ensuring endpoints return clear and consistent status codes (400, 401, 403, 429, 500).

### Code Quality

- Improved error handling consistency across all endpoints.
- Added/updated docstrings and type hints for security-related code.
- Updated and clarified documentation for new security features.
- Added comprehensive unit tests for security features
- Implemented proper logging configuration
- Fixed JavaScript linting issues
- Added proper error handling in frontend code
- Improved code organization and modularity

### Documentation

- Updated README with security features
- Added detailed API documentation
- Updated dependency documentation

## [0.5.0] - 2025-06-30

### Added

- Initial release with basic functionality
- Web-based terminal interface
- AI-powered chat assistant
- Level progression tracking
- Secure session management

[0.5.1]: https://github.com/TheRealFredP3D/Making-BanditGUI/compare/v0.5.0...v0.5.1
[0.5.0]: https://github.com/TheRealFredP3D/Making-BanditGUI/releases/tag/v0.5.0
