# Contributing to BanditGUI

Thank you for your interest in contributing to BanditGUI! This document will help you get started and understand the project's architecture and how to get involved.

---

## Security Guidelines

### Security Policy
- All contributions must adhere to security best practices
- Security vulnerabilities should be reported <NAME_EMAIL>
- Security patches should be tested thoroughly before submission
- All security-related changes require review by at least two maintainers

### Security Requirements
- All code must be tested for security vulnerabilities
- Input validation and sanitization must be implemented
- Proper error handling for security failures is required
- Session management must follow security best practices
- API endpoints must implement rate limiting and CSRF protection

### New Security Features
- All new endpoints must include defensive session and CSRF checks.
- Endpoints should return clear and appropriate status codes for security failures:
  - 400 for bad requests (e.g., missing/invalid CSRF token)
  - 401 for unauthorized (e.g., missing/invalid session)
  - 403 for forbidden (e.g., permission denied)
  - 429 for rate limiting
  - 500 for server errors
- When updating or adding security features, update the security documentation and audit files.

## Testing Guidelines

### Unit Tests
- All new features must be accompanied by unit tests
- Tests should cover both success and failure cases
- Mock external dependencies where possible
- Use descriptive test names and assertions

### Integration Tests
- Critical workflows must be covered by integration tests
- Tests should verify end-to-end functionality
- Environment variables should be properly mocked
- Database interactions should be tested with test data

### Security Tests
- All security features must be tested
- Rate limiting should be verified
- CSRF protection must be tested
- Session management should be validated
- API key security must be confirmed

## Code Quality

### Code Style
- Follow PEP 8 style guidelines
- Use type hints where appropriate
- Maintain consistent naming conventions
- Keep functions and classes focused and modular

### Documentation
- All public functions must have docstrings
- Complex logic should be well documented
- Security considerations must be documented
- API endpoints should have clear documentation

## Pull Request Process

1. Fork the repository and create your branch from `main`
2. Ensure all tests pass before submitting
3. Include appropriate test coverage
4. Document any security considerations
5. Update relevant documentation
6. Request a review from at least one maintainer

- [ ] Does your code return the correct status code for all security-related errors?
- [ ] Have you updated the security documentation and audit docs if you changed or added security features?

---