# Project Dependencies

| Package         | Purpose                                      | Link                                         |
|-----------------|----------------------------------------------|----------------------------------------------|
| flask           | Web framework for the backend API            | <https://palletsprojects.com/p/flask/>         |
| paramiko        | SSH connections to remote servers            | <https://www.paramiko.org/>                    |
| python-dotenv   | Loads environment variables from .env files  | <https://github.com/theskumar/python-dotenv>   |
| requests        | HTTP requests library                        | <https://docs.python-requests.org/>            |
| beautifulsoup4  | HTML/XML parsing                             | <https://www.crummy.com/software/BeautifulSoup/>|
| litellm         | LLM API integration                          | <https://github.com/BerriAI/litellm>           |
| pytest          | Testing framework                            | <https://docs.pytest.org/>                     |
| pytest-mock     | Mocking plugin for pytest                    | <https://github.com/pytest-dev/pytest-mock>    |
