# BanditGUI Development Environment Setup

This document explains the development environment setup script located at `_TODO_/env-setup/setup.sh`.

## Script Overview

This bash script automates the setup of a complete development environment for the BanditGUI project, which is a Python application with a Node.js frontend.

## Key Components

### 1. System Dependencies (Lines 6-14)

- Updates the system package manager (`apt-get update`)
- Installs Python 3, pip, and venv for Python development
- Installs Node.js 18.x and npm for frontend development

### 2. Installation Verification (Lines 16-20)

- Displays versions of installed tools to confirm successful installation
- Shows Python, pip, Node.js, and npm versions

### 3. Python Environment Setup (Lines 22-36)

- Creates a Python virtual environment in the `venv` directory
- Activates the virtual environment
- **Automatically configures the environment** by adding activation commands to `~/.profile`
- Adds the project directory to `PYTHONPATH` so the `banditgui` module can be imported

### 4. Dependency Installation (Lines 38-47)

- Upgrades pip to the latest version within the virtual environment
- Installs Python dependencies from `requirements.txt`
- Installs Node.js dependencies from `package.json`

### 5. Frontend Build (Lines 49-51)

- Builds frontend assets using `npm run build`

### 6. Test Environment Setup (Lines 53-65)

- Creates necessary directories for the application
- Sets up a default LLM model configuration file (`banditgui/config/llm_model.json`) with sample models
- Creates an empty `.env` file if it doesn't exist

## Important Features

- **Error handling**: Uses `set -e` to exit on any command failure
- **Persistent configuration**: Modifies `~/.profile` so the virtual environment is automatically activated in future sessions
- **Complete automation**: Sets up everything needed for development in one run
- **Safe execution**: Checks for existing files before creating them

## Usage

To run the setup script:

```bash
cd /path/to/BanditGUI/env-setup
chmod +x setup.sh
./setup.sh
```

## What Gets Installed

### System Packages

- Python 3
- pip (Python package manager)
- python3-venv (virtual environment support)
- Node.js 18.x
- npm (Node.js package manager)

### Python Environment

- Virtual environment in `./venv/`
- All packages from `requirements.txt`
- Project directory added to PYTHONPATH

### Node.js Environment

- All packages from `package.json`
- Built frontend assets

### Configuration Files Created

- `banditgui/config/llm_model.json` - Default LLM model configuration
- `.env` - Empty environment file for local configuration

## Post-Setup

After running this script:

1. The virtual environment will be automatically activated in new terminal sessions
2. The `banditgui` module will be importable from anywhere in the project
3. All dependencies will be installed and ready to use
4. Frontend assets will be built and ready for development

This script is designed to get a new developer up and running with the BanditGUI project quickly, handling both backend (Python) and frontend (Node.js) setup requirements.
