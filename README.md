# BanditGUI: A Web Interface for OverTheWire Bandit - v0.5.1

![BanditGUI Badge](BanditGUI-Badge.jpg)  

---

![Screenshot](docs/assets/v0.4-Main-screen.jpg)  

BanditGUI is a web-based interface for the popular OverTheWire Bandit wargame. It aims to make learning cybersecurity fundamentals more approachable by providing a browser-based terminal, structured level information, and an AI-powered assistant.

---

## Why BanditGUI?

The command-line nature of challenges like Bandit can be a hurdle for beginners. BanditGUI lowers this barrier by:

- Providing an integrated terminal in your browser.
- Offering easy access to level goals, commands, and learning resources.
- Assisting with hints and explanations through an AI chat interface.

## Core Features
  
![Overview](docs/drawio/v0.5.0-Overview.png)  

- **Interactive Web Terminal:** A full-featured xterm.js terminal in your browser to connect to Bandit via SSH.
- **Real SSH Connections:** Practice with actual Linux commands in a realistic environment.
- **Level Guidance:** Access information, relevant commands, and reading materials for each Bandit level.
- **AI-Powered Chat Assistant:** Get intelligent hints and explanations from an LLM-powered chat (supports various models via LiteLLM).
- **Progress Tracking:** Comprehensive system that tracks:
  - Time taken per level
  - Commands used
  - Hints utilized
  - Completion statistics
  - Automatic progression to next levels
  - Command efficiency metrics
  - Learning patterns analysis
- **Theme System:** Multiple themes including:
  - Dark theme
  - Light theme
  - High-contrast theme (WCAG compliant)
  - Retro theme
- **Built-in Command Cheatsheet:** Quick reference for common commands
- **Modular Design:** Built with a maintainable Python Flask backend and JavaScript frontend
- **Accessibility Features:**
  - Full keyboard navigation
  - Screen reader support
  - High-contrast theme
  - Adjustable font sizes
  - Color contrast optimization

## Progress Tracking System

BanditGUI includes a comprehensive progress tracking system that helps you monitor your learning journey:

### Features

1. **Level Completion Detection**
   - Automatic detection of level completion via regex pattern matching
   - Recovery and storage of level passwords
   - Display of completion statistics
   - Automatic progression to next levels

2. **Statistics Tracking**
   - Time taken per level
   - Number of commands used
   - Hint usage statistics
   - Command efficiency metrics
   - Level completion rate

3. **Progress Visualization**
   - Completion percentage across all levels
   - Most used commands
   - Command efficiency scores
   - Learning patterns analysis

4. **Level Progression**
   - Automatic SSH connection management
   - Password recovery and storage
   - Next level connection information
   - Session-based progress tracking

### How It Works

1. When you complete a level:
   - The system detects completion via the level completion message
   - Automatically extracts and stores the recovered password
   - Calculates time taken and commands used
   - Displays a congratulatory message with statistics
   - Provides next level connection information
   - Updates your progress tracking

2. Progress is automatically saved:
   - Level completion times
   - Commands used
   - Hint usage
   - Overall completion rate

3. The system helps you progress:
   - Automatically closes current SSH connection
   - Provides next level connection details
   - Clears terminal history for next level
   - Maintains password recovery for future reference

### Benefits

- **Learning Analytics:** Track your progress and identify areas for improvement
- **Motivation:** See your improvement over time
- **Efficiency:** Learn from your command usage patterns
- **Security:** Secure password storage and management
- **Guidance:** Clear progression path through all levels

## Tech Stack

- **Backend:** Python (Flask with Flask-Session)
- **Frontend:** HTML, CSS, JavaScript (xterm.js)
- **SSH:** Paramiko
- **LLM Integration:** LiteLLM
- **Security:**
  - Flask-Session for secure sessions
  - Flask-WTF for CSRF protection
  - Rate limiter middleware
  - Secure cookie handling

![Easy LLM Selection](docs/assets/v0.4-Ask-a-Pro-LLM.jpg)

## Getting Started

### Installation

1. **Clone the repository:**

    ```bash
    git clone https://github.com/therealfredp3D/Making-BanditGUI.git
    cd Making-BanditGUI
    ```

2. **Set up environment variables:**
   Create a `.env` file in the root directory and add your API keys:

    ```bash
    # .env file
    FLASK_SECRET_KEY=your-secret-key-here
    OPENAI_API_KEY=your-openai-key
    # Add other provider API keys as needed
    ```

3. **Run the installation script:**
    This script will set up a virtual environment, install dependencies, and create run scripts.

    ```bash
    python install.py
    # or python3 install.py on some systems
    ```

4. **Follow on-screen instructions.** The script will guide you through any necessary checks.

### Security Requirements

- Ensure your `.env` file is never committed to version control
- Use strong, unique session secret keys
- Keep API keys secure and rotate them regularly
- Regularly update dependencies to latest secure versions

### Running BanditGUI

- **Windows:** Execute `run.bat` (Generated by `install.py`)
- **Linux/macOS:** Execute `./run.sh` (Generated by `install.py`)

Once started, the application is typically available at `http://127.0.0.1:5000`.

## How to Contribute

We welcome contributions! Please follow these general steps:

1. Fork the repository.
2. Create a new branch for your feature or bug fix.
3. Make your changes.
4. Test your changes thoroughly.
   - Ensure security features are maintained
   - Verify session handling
   - Test API rate limiting
5. Commit your changes with a clear message.
6. Open a Pull Request to the `main` branch.

### Security Considerations for Contributors

- Always validate user input
- Follow secure coding practices
- Maintain proper session handling
- Respect rate limits
- Never commit sensitive information

## Future Development Goals

1. **Enhanced AI Integration**
   - Complete intelligent command explanation system
   - Implement progress analysis
   - Create personalized learning paths

2. **Gamification Elements**
   - Achievement system
   - Visual progress tracking
   - Daily challenges
   - Streaks and badges

3. **Technical Improvements**
   - Complete end-to-end testing suite
   - Session recording functionality
   - Performance metrics visualization
   - Plugin system for custom CTF challenges

4. **Social Features**
   - Challenge sharing
   - Race mode
   - Ask a Friend feature
   - Streaming overlay support

5. **Accessibility Enhancements**
   - Complete screen reader support
   - Customizable panel layouts
   - Improved image alt text
   - Better keyboard shortcuts

## Roadmap Highlights

We are continuously working to improve BanditGUI. Some of our future goals include:

1. **Password Management**: Adding secure password storage with encryption
2. **Progress Tracking**: Implementing a system to track user progress through the challenges
3. **Gamification**: Adding badges, streaks, and other gamification elements to increase engagement

## License

This project is licensed under the MIT License.

### 5. LLM-Powered Chat Interface

The application includes an advanced chat interface powered by Large Language Models (LLMs). It leverages `litellm` to support various API providers and LLMs, making it easy to switch between different models.

**Dynamic Ollama Model Loading:**

- When selecting an Ollama model, the app automatically fetches the list of available models from your local Ollama server (`http://localhost:11434/api/tags`).
- This ensures the dropdown always reflects the actual models you have installed in Ollama, without needing to update the static config file.

For more detailed installation instructions, see [installation guide](docs/notion/notion_installation_guide.md).

## New in v0.4

### Progress Tracking System

BanditGUI now includes a comprehensive progress tracking system that helps you monitor your learning journey:

1. **Level Completion Detection**
   - Automatic detection of level completion via regex pattern matching
   - Recovery and storage of level passwords
   - Display of completion statistics
   - Automatic progression to next levels

2. **Statistics Tracking**
   - Time taken per level
   - Number of commands used
   - Hint usage statistics
   - Command efficiency metrics
   - Level completion rate

3. **Progress Visualization**
   - Completion percentage across all levels
   - Most used commands
   - Command efficiency scores
   - Learning patterns analysis

4. **Level Progression**
   - Automatic SSH connection management
   - Password recovery and storage
   - Next level connection information
   - Session-based progress tracking

### Frontend Asset Bundling (v0.4.2+)

- The frontend now uses Webpack to bundle and minify JavaScript and CSS assets for improved performance.
- All main JS and CSS are bundled into `banditgui/static/dist/main.js` and `banditgui/static/dist/styles.css`.
- The main HTML template (`banditgui/templates/index.html`) now references these bundled files.

### How to Build Frontend Assets

1. Install dependencies (if not already):

   ```bash
   npm install
   ```

2. Build the assets:

   ```bash
   npm run build
   ```

3. The output will be in `banditgui/static/dist/`.

> **Note:** If you add or change JS/CSS, re-run `npm run build` to update the bundles.

---

## Security Features

BanditGUI implements robust security measures to protect user data and ensure a secure learning environment:

- **Session Security**
  - Secure cookie-based sessions with configurable timeouts
  - Automatic session refresh and last activity tracking
  - CSRF protection middleware with token validation
  - Secure session cookies (HTTPOnly, Secure, SameSite=Lax)
  - Structured session data validation using JSON Schema
  - Session state persistence with integrity checks
  - Session-based progress tracking with data validation
  - Secure session cleanup on timeout

- **API Security**
  - Token bucket-based rate limiting per IP and endpoint
  - Configurable rate limits with automatic refilling
  - Secure API key management with automatic rotation
  - Environment variable-based API key storage
  - Generic error responses to prevent information leakage
  - Protection against command injection
  - JSON schema validation for all API endpoints
  - Secure session management middleware

- **Data Protection**
  - Secure storage of session data with JSON Schema validation
  - Input validation for all user inputs
  - Protection against XSS and CSRF attacks
  - Secure password handling and storage
  - Strict JSON schema validation with error recovery
  - Regular data integrity checks with validation
  - Progress data encryption at rest
  - Secure command history storage

- **Monitoring and Logging**
  - Comprehensive security-specific logging system
  - Detailed logging of authentication attempts
  - Rate limit tracking and monitoring
  - CSRF token validation logging
  - API request logging with detailed context
  - Session activity tracking with audit trails
  - Performance metrics collection
  - Critical operation logging with security context

- **Error Handling**
  - Structured error responses with error types and context
  - User-friendly error messages with graceful degradation
  - Detailed logging for debugging with stack traces
  - Error type information in responses
  - Graceful degradation for critical failures
  - Automatic error recovery mechanisms
  - Progress data backup and recovery
  - Custom error types for security events

- **Security Logging**
  - Security-specific logging with structured data
  - Detailed event tracking with timestamps
  - Request context logging (IP, user agent, path)
  - Operation status logging (success/failure)
  - Detailed error information in logs
  - Separate security log directory
  - Configurable log levels for security events

## Security Considerations

### Data Storage Security

- All sensitive data is stored with encryption at rest
- Progress data is validated against strict JSON schemas
- Regular integrity checks are performed on stored data
- Automatic backup system for user progress
- Secure password hashing with strong algorithms

### Error Handling Best Practices

- Structured error responses with error types and context
- Generic error messages to prevent information leakage
- Detailed logging for debugging with stack traces
- Graceful degradation for critical failures
- Automatic error recovery mechanisms

### Session Management

- Secure cookie-based sessions with automatic timeouts
- CSRF protection on all endpoints
- Session refresh mechanism
- Session state persistence with integrity checks
- Secure password storage and handling

### API Security

- Rate limiting per endpoint to prevent abuse
- Input validation and sanitization
- Secure API key handling from environment variables
- Protection against command injection
- JSON schema validation for all API requests

### Data Validation

- Strict JSON schema validation for all data
- Input validation for all user inputs
- Protection against XSS and CSRF attacks
- Regular data integrity checks
- Progress data validation before storage

### Monitoring and Logging

- Comprehensive logging of all operations
- Security event tracking with detailed context
- Error tracking with full stack traces
- Session activity monitoring
- Progress tracking audit logs
- Performance metrics collection

## Author

BanditGUI is developed and maintained by Frederick Pellerin.

For any questions or concerns, please contact me at <<EMAIL>> or on:

Github: [TheRealFREDP3D](https://github.com/therealfredp3d)  
X: [@therealfredp3D](https://x.com/therealfredp3D)  
Medium: [@TheRealFREDP3D](https://medium.com/@therealfredp3d/)
