# Chat Module

This module provides chat functionality and message management for BanditGUI.

## Overview

The Chat module is designed to handle all chat-related operations within BanditGUI, including:

- Message management and storage
- Chat history tracking
- Level-based context
- System and user message handling
- Hint system for different levels

## Key Components

### ChatManager Class

The main class that handles all chat operations. It provides the following core functionalities:

#### Message Management

- `add_message(message: str, level: Optional[int] = None, is_system: bool = False)`:
  - Adds a new message to the chat history
  - Supports both user and system messages
  - Automatically timestamps messages
  - Can associate messages with specific levels

#### Message Retrieval

- `get_messages(level: Optional[int] = None, count: int = 50)`:
  - Retrieves recent messages from chat history
  - Can filter messages by level
  - Supports pagination through count parameter

#### Level Management

- `set_current_level(level: Optional[int])`:
  - Sets the current chat context level
  - Automatically adds system messages when changing levels

#### History Management

- `clear_messages(level: Optional[int] = None)`:
  - Clears messages from chat history
  - Can clear messages for a specific level or all messages

#### Hint System

- `get_hint(level: int)`:
  - Provides level-specific hints
  - Includes predefined hints for different levels
  - Returns appropriate hints based on current level

## Features

### Message Tracking

- Maintains both global and level-specific message history
- Timestamps all messages
- Supports both system and user messages
- Proper message filtering and retrieval

### Level Context

- Maintains chat context per level
- Automatic system messages when changing levels
- Level-specific message storage
- Level-specific hints

### Timestamping

- All messages are automatically timestamped
- Uses ISO 8601 format for timestamps
- Includes date and time information

## Usage Example

```python
from banditgui.chat import ChatManager

# Initialize the chat manager
chat = ChatManager()

# Set current level
chat.set_current_level(1)

# Add user message
chat.add_message("Hello, what should I do next?", level=1)

# Add system message
chat.add_message("Welcome to level 1!", level=1, is_system=True)

# Get recent messages
messages = chat.get_messages(level=1)
for msg in messages:
    print(f"[{msg['timestamp']}] {'SYS' if msg['is_system'] else 'USER'}: {msg['content']}")

# Get level hint
hint = chat.get_hint(1)
print(f"Hint: {hint}")

# Clear messages for current level
chat.clear_messages(level=1)
```
