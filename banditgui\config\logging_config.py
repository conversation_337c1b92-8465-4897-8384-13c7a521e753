import logging
import os
from datetime import datetime

# Create logs directory if it doesn't exist
LOGS_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
os.makedirs(LOGS_DIR, exist_ok=True)

# Create a rotating file handler that creates a new log file each day
log_file = os.path.join(LOGS_DIR, f'banditgui_{datetime.now().strftime("%Y%m%d")}.log')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)

# Create specific loggers
logger = logging.getLogger('banditgui')
logger.setLevel(logging.INFO)

# Add specific handlers for different components
progression_logger = logging.getLogger('banditgui.progression')
progression_logger.setLevel(logging.INFO)

security_logger = logging.getLogger('banditgui.security')
security_logger.setLevel(logging.INFO)

api_logger = logging.getLogger('banditgui.api')
api_logger.setLevel(logging.INFO)

# Add specific formatter for API logs
api_formatter = logging.Formatter(
    '%(asctime)s - %(levelname)s - %(message)s - %(user)s - %(ip)s'
)
api_handler = logging.FileHandler(os.path.join(LOGS_DIR, 'api_requests.log'))
api_handler.setFormatter(api_formatter)
api_logger.addHandler(api_handler)

# Add error handler with traceback
error_handler = logging.FileHandler(os.path.join(LOGS_DIR, 'error.log'))
error_handler.setLevel(logging.ERROR)
error_handler.setFormatter(
    logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s\n%(exc_info)s'
    )
)
logger.addHandler(error_handler)

# Add security-specific logging
security_handler = logging.FileHandler(os.path.join(LOGS_DIR, 'security.log'))
security_handler.setLevel(logging.INFO)
security_handler.setFormatter(
    logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s - %(user)s - %(ip)s - %(request_id)s'
    )
)
security_logger.addHandler(security_handler)

# Add audit logging
audit_logger = logging.getLogger('banditgui.audit')
audit_logger.setLevel(logging.INFO)
audit_handler = logging.FileHandler(os.path.join(LOGS_DIR, 'audit.log'))
audit_handler.setLevel(logging.INFO)
audit_handler.setFormatter(
    logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s - %(user)s - %(action)s - %(result)s'
    )
)
audit_logger.addHandler(audit_handler)

# Example usage:
# logger.info('This is an info message')
# security_logger.warning('Security warning')
# api_logger.error('API error', extra={'user': 'test', 'ip': '127.0.0.1'})
# audit_logger.info('User login', extra={'user': 'test', 'action': 'login', 'result': 'success'})

def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance with the specified name.
    
    Args:
        name: The name of the logger
        
    Returns:
        A configured logger instance
    """
    # Get or create logger with the specified name
    log_name = f'banditgui.{name}'
    return logging.getLogger(log_name)
