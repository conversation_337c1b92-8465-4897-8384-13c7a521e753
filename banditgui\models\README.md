# BanditGUI Models

This directory contains the core data models for BanditGUI, defining the structure and behavior of player data and game progression.

## Directory Structure

- `__pycache__`: Contains Python bytecode cache files (automatically generated)
- `progression.py`: Main data models for player progression and statistics

## Implementation Overview

The models are implemented using Python's `dataclass` decorator for automatic generation of special methods like `__init__`, `__repr__`, and `__eq__`. This makes the code more concise and maintainable while providing a clear structure for player data.

## Data Models

### PlayerSession

The `PlayerSession` class represents a single gaming session and tracks:

- `session_id`: Unique identifier for the session (UUID)
- `start_time`: When the session began (ISO format)
- `current_level`: The player's current progress (integer)
- `completed_levels`: List of levels the player has completed (List[int])
- `command_history`: Record of commands used during the session (List[Dict])
- `hints_used`: Number of hints used per level (Dict[int, int])
- `time_spent`: Time spent on each level in seconds (Dict[int, float])
- `achievements`: List of achievements earned (List[str])
- `notes`: Personal notes made by the player per level (Dict[int, str])

### PlayerStats

The `PlayerStats` class maintains long-term player statistics and progress:

- `total_levels_completed`: Total number of levels finished (int)
- `total_time_spent`: Cumulative time spent playing in seconds (float)
- `commands_mastered`: List of commands the player has mastered (List[str])
- `streak_days`: Number of consecutive days played (int)
- `last_played`: Timestamp of the last play session (datetime)
- `favorite_levels`: Player's preferred levels (List[int])
- `difficulty_preference`: Player's preferred difficulty level ('beginner', 'intermediate', 'advanced')

## Usage in the Application

These models are used throughout the application to track and manage player progress, statistics, and game state. They provide a standardized way to store and retrieve player data, enabling features like:

- Progress tracking through `ProgressionManager`
- Achievement system integration
- Command mastery tracking
- Difficulty level preferences
- Session history management
- Personal notes system

## Integration Points

The models integrate with several key components:

1. **Backend**:
   - `ProgressionManager` for session management
   - API endpoints for progress updates
   - Secure storage of player data

2. **Frontend**:
   - `BanditApp` class for client-side state management
   - Local storage for session persistence
   - UI updates for progress indicators

3. **Security Features**:
   - CSRF token validation
   - Session validation
   - Input sanitization
   - Rate limiting

## Future Extensions

The models are designed to be extensible, allowing for future additions such as:

- Additional achievement types
- More detailed command statistics
- Expanded difficulty preferences
- Enhanced session tracking features
- New player metrics and analytics
- Integration with gamification features
- Support for multiple game modes

## Best Practices

When working with these models:

1. Always validate input data
2. Use proper error handling
3. Maintain session consistency
4. Follow security best practices
5. Document any changes to the schema
6. Test thoroughly after modifications

## Error Handling

The models include robust error handling through:

- Type validation using Python's typing system
- Data validation using custom validators
- Exception handling for data persistence
- Logging for debugging and monitoring

## Security Considerations

- All sensitive data is properly encrypted
- Session IDs are securely generated
- Input is sanitized before storage
- Access is controlled through proper authentication
- Data integrity is maintained through validation

## Performance Considerations

- Efficient data storage format
- Optimized query patterns
- Caching strategies for frequently accessed data
- Proper indexing for quick lookups

## Testing

The models include comprehensive test coverage through:

- Unit tests for individual components
- Integration tests for model interactions
- Performance tests for large datasets
- Security tests for data protection

## Example Usage

```python
# Creating a new session
session = PlayerSession(
    session_id="uuid-123",
    start_time=datetime.now().isoformat(),
    current_level=0,
    completed_levels=[0],
    command_history=[{"command": "ls", "timestamp": datetime.now().isoformat()}],
    hints_used={0: 0},
    time_spent={0: 0.0},
    achievements=[],
    notes={}
)

# Updating player stats
stats = PlayerStats(
    total_levels_completed=1,
    total_time_spent=300.5,
    commands_mastered=["ls", "cd", "pwd"],
    streak_days=1,
    last_played=datetime.now(),
    favorite_levels=[0],
    difficulty_preference="beginner"
)
```



## Usage

These models are used throughout the application to track and manage player progress, statistics, and game state. They provide a standardized way to store and retrieve player data, enabling features like:

- Progress tracking
- Achievement system
- Command mastery tracking
- Difficulty level preferences
- Session history

## Future Extensions

The models are designed to be extensible, allowing for future additions such as:

- Additional achievement types
- More detailed command statistics
- Expanded difficulty preferences
- Enhanced session tracking features
- New player metrics and analytics
