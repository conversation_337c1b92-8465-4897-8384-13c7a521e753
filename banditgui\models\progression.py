from dataclasses import dataclass
from datetime import datetime
from typing import Dict, List


@dataclass
class PlayerSession:
    session_id: str
    start_time: str
    current_level: int
    completed_levels: List[int]
    command_history: List[Dict]
    hints_used: Dict[int, int]  # level -> hint count
    time_spent: Dict[int, float]  # level -> seconds
    achievements: List[str]
    notes: Dict[int, str]  # level -> personal notes

@dataclass
class PlayerStats:
    total_levels_completed: int
    total_time_spent: float
    commands_mastered: List[str]
    streak_days: int
    last_played: datetime
    favorite_levels: List[int]
    difficulty_preference: str  # 'beginner', 'intermediate', 'advanced' 