# SSH Module

This module provides SSH connectivity and management functionality for BanditGUI.

## Overview

The SSH module is designed to handle all SSH-related operations within BanditGUI, including:

- Establishing SSH connections
- Executing remote commands
- Checking server status
- Managing SSH sessions

## Key Components

### SSHManager Class

The main class that handles all SSH operations. It provides the following core functionalities:

#### Connection Management

- `connect(username=None, password=None)`:
  - Establishes an SSH connection to the configured server
  - Supports both default and custom credentials
  - Implements proper error handling and logging

#### Command Execution

- `execute_command(command: str, username=None, password=None)`:
  - Executes commands on the remote SSH server
  - Automatically connects if not already connected
  - <PERSON>les command output and errors
  - Includes detailed logging of operations

#### Server Status

- `check_server_status()`:
  - Verifies if the SSH server is online
  - Returns detailed status information
  - Implements timeout for connection attempts

#### Session Management

- `close()`:
  - Properly closes the SSH connection
  - Cleans up resources

## Configuration

The SSH module uses configuration from the project's settings system (`banditgui.config.settings`). Required configuration parameters include:

- `ssh_host`: The SSH server hostname/IP
- `ssh_port`: The SSH server port
- `ssh_username`: Default SSH username
- `ssh_password`: Default SSH password

## Error Handling

The module implements comprehensive error handling for all operations, including:

- Connection failures
- Authentication issues
- Command execution errors
- Network timeouts

All errors are logged using the project's logging system and include detailed context information.

## Security Features

- Uses `paramiko.AutoAddPolicy` for host key management
- Implements proper connection timeouts
- Secure credential handling
- Comprehensive logging for auditing purposes

## Usage Example

```python
from banditgui.ssh import SSHManager

# Initialize the manager
ssh = SSHManager()

# Connect to the server
result = ssh.connect()
if result is not True:
    print(f"Connection failed: {result}")
    exit(1)

# Execute a command
output = ssh.execute_command("ls -la")
print(output)

# Close the connection
ssh.close()
```
