# BanditGUI Static Assets

This directory contains all the static assets used by BanditGUI, including CSS stylesheets, JavaScript files, and other static resources.

## Directory Structure

```
static/
├── bandit-terminal.css      # Main terminal styling
├── css/                     # Additional CSS files
├── dist/                    # Distribution files
├── js/                      # JavaScript files
├── xterm-custom.css         # Custom xterm.js styling
└── xterm.css               # Base xterm.js styling
```

## Key Files

### bandit-terminal.css

The main CSS file that contains:

- Global styles and resets
- Modern dark theme color variables
- Terminal container styling
- Chat input container styling
- Error message styling

### xterm-custom.css

Custom styling for the xterm.js terminal emulator, enhancing the default styling.

### xterm.css

Base CSS file for xterm.js, providing essential terminal emulator styling.

## CSS Organization

The CSS files are organized to provide a modular approach to styling:

- `bandit-terminal.css` contains the main application styles
- `xterm-custom.css` provides custom terminal-specific styling
- Additional CSS files are organized in the `css/` directory

## JavaScript Files

The `js/` directory contains all the JavaScript files used by the application. These files handle:

- Terminal functionality
- UI interactions
- Event handling
- API calls

## Distribution Files

The `dist/` directory contains distribution files that are generated during the build process. These files should not be modified directly.

## Usage

All static assets in this directory are served by the web application and are referenced in the HTML templates. The CSS files are imported in the main HTML file, and JavaScript files are loaded as needed.

## Development Notes

- CSS files use CSS variables for consistent theming
- JavaScript files follow modular patterns
- All static assets should be minified before production deployment
- The `dist/` directory should be included in `.gitignore` as it contains build artifacts
