.password-prompt {
    position: fixed;
    left: 50%;
    top: 20%;
    transform: translate(-50%, 0);
    z-index: 1000;
    background: #222;
    color: #bada55;
    border: 2px solid #bada55;
    border-radius: 8px;
    padding: 1.5em 2em;
    box-shadow: 0 4px 24px rgba(0,0,0,0.5);
    font-family: monospace;
    text-align: center;
}
.password-prompt button {
    margin-top: 1em;
    background: #333;
    color: #bada55;
    border: none;
    border-radius: 4px;
    padding: 0.5em 1.5em;
    font-size: 1.1em;
    cursor: pointer;
    transition: background 0.2s;
}
.password-prompt button:hover {
    background: #444;
}

.mentor-message {
    background-color: var(--primary-light, #282828); /* Match chat background */
    border-left: 4px solid #4CAF50; /* Green accent bar */
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 0 6px 6px 0;
    box-shadow: var(--shadow-sm);
    color: var(--text-primary, #F5F5F5); /* Light text */
}

.mentor-message .mentor-label {
    font-weight: bold;
    color: #4CAF50;
    margin-bottom: 8px;
    letter-spacing: 0.5px;
}

.mentor-message .message-content {
    color: var(--text-primary, #F5F5F5); /* Ensure content is light */
} 