/**
 * BanditGUI Application
 * Author: <PERSON> <<EMAIL>>
 * Github: https://github.com/TheRealFRED3D
 * 
 * This script handles both the chat interface and the terminal functionality.
 */

class BanditApp {
  constructor() {
    // Initialize state
    this.isConnected = false;
    this.currentLevel = null;
    this.currentLevelDescription = null; // Added for Ask-a-Pro
    this.serverStatus = "unknown";
    this.foundPasswords = {};
    this.csrfToken = null;
    this.sessionTimeout = null;

    // Initialize session
    this.sessionId = this.getSessionId();

    // Restore found passwords from session
    this.restorePasswordsFromStorage();

    // Initialize CSRF token
    this.initCSRF();

    // Initialize components
    this.initChat();
    this.initTerminal();

    // Set up event listeners
    this.setupEventListeners();

    // Set up periodic server status check (after initial check in welcome message)
    setInterval(() => this.checkServerStatus(), 600000); // Check every 60 seconds

    // Add session refresh check
    setInterval(() => this.refreshSession(), 15000); // Refresh session every 15 seconds

    // Add Resume Progress button
    this.addResumeProgressButton();
  }

  // Add getSessionId method to fix missing function error
  getSessionId() {
    // Try to get an existing session ID from localStorage
    let sessionId = localStorage.getItem("banditSessionId");
    if (!sessionId) {
      // Generate a new UUID (simple version)
      sessionId = ([1e7] + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, (c) =>
        (
          c ^
          (crypto.getRandomValues(new Uint8Array(1))[0] & (15 >> (c / 4)))
        ).toString(16)
      );
      localStorage.setItem("banditSessionId", sessionId);
    }
    return sessionId;
  }

  /**
   * Initialize CSRF token
   */
  async initCSRF() {
    try {
      const response = await fetch("/test-csrf-token");
      const data = await response.json();
      sessionStorage.setItem("csrfToken", data.csrf_token);
    } catch (error) {
      console.error("Failed to initialize CSRF token:", error);
    }
  }

  /**
   * Restore found passwords from local storage
   */
  restorePasswordsFromStorage() {
    try {
      const progress = JSON.parse(localStorage.getItem("banditProgress"));
      if (progress && progress.foundPasswords) {
        this.foundPasswords = progress.foundPasswords;
      }
    } catch (error) {
      console.error("Failed to restore passwords from storage:", error);
      this.foundPasswords = {};
    }
  }

  /**
   * Add resume progress button
   */
  addResumeProgressButton() {
    // Check if there are any saved passwords
    if (Object.keys(this.foundPasswords).length > 0) {
      const resumeContainer = document.createElement("div");
      resumeContainer.className = "resume-progress-container";
      resumeContainer.innerHTML = `
                <h4>Resume Progress</h4>
                <p>You have saved passwords for levels: ${Object.keys(
                  this.foundPasswords
                ).join(", ")}</p>
            `;

      Object.keys(this.foundPasswords).forEach((level) => {
        const resumeBtn = document.createElement("button");
        resumeBtn.textContent = `Resume Level ${level}`;
        resumeBtn.className = "resume-level-button";
        resumeBtn.onclick = () => this.showResumeModal(level);
        resumeContainer.appendChild(resumeBtn);
      });

      // Add to chat messages
      this.chatMessages.appendChild(resumeContainer);
    }
  }

  /**
   * Refresh session to prevent timeout
   */
  async refreshSession() {
    try {
      const csrfToken = sessionStorage.getItem("csrfToken");
      if (csrfToken) {
        // Make a lightweight request to refresh session
        await fetch("/server-status", {
          method: "GET",
          headers: {
            "X-CSRF-Token": csrfToken,
          },
        });
      }
    } catch (error) {
      // Silently handle session refresh errors
      console.debug("Session refresh failed:", error);
    }
  }

  /**
   * Connect to next level with password
   */
  async connectToNextLevel(level, password) {
    try {
      const sshCommand = `ssh bandit${level}@bandit.labs.overthewire.org -p 2220`;
      await this.executeCommand(sshCommand, { password: password });
      this.currentLevel = level;
      this.addAssistantMessage(
        `Connected to Level ${level}. You can now execute commands on the server.`
      );
    } catch (error) {
      console.error("Failed to connect to next level:", error);
      this.addAssistantMessage(
        `Failed to connect to Level ${level}: ${error.message}`
      );
    }
  }

  /**
   * Write a line to the terminal
   */
  writeLine(text) {
    this.term.write(text + "\r\n");
  }

  /**
   * Write prompt to terminal
   */
  writePrompt() {
    this.term.write(this.prompt);
  }

  /**
   * Update connection status UI
   */
  updateConnectionStatus() {
    const statusIndicator = document.getElementById("status-indicator");
    const statusText = document.getElementById("status-text");

    if (this.isConnected) {
      statusIndicator.className = "connected";
      statusText.textContent = `Connected (Level ${
        this.currentLevel || "Unknown"
      })`;
    } else {
      statusIndicator.className = "disconnected";
      statusText.textContent = "Disconnected";
    }
  }

  /**
   * Check server status
   */
  async checkServerStatus() {
    try {
      const response = await fetch("/server-status");
      const data = await response.json();

      if (data.status === "success") {
        this.serverStatus = data.serverStatus.status;
      } else {
        this.serverStatus = "error";
      }
    } catch (error) {
      this.serverStatus = "error";
      console.error("Failed to check server status:", error);
    }
  }

  /**
   * Display terminal welcome message
   */
  async displayTerminalWelcome() {
    try {
      // Check server status first
      await this.checkServerStatus();

      const response = await fetch("/quotes/welcome?count=1");
      const data = await response.json();

      let welcomeMessage = "\r\n\x1b[36m" + "=".repeat(82) + "\x1b[0m\r\n";
      welcomeMessage +=
        "\x1b[33m           Terminal\x1b[0m\r\n";
      welcomeMessage += "\x1b[36m" + "=".repeat(82) + "\x1b[0m\r\n\r\n";

      if (data.status === "success" && data.quotes) {
        // Display the single quote
        const quote = data.quotes[0];
        welcomeMessage += `\x1b[37mQuote of the Day:\r\n${quote}\x1b[0m\r\n\r\n`;
      }

      welcomeMessage += `\x1b[37mServer Status: ${
        this.serverStatus === "online" ? "\x1b[32mOnline" : "\x1b[31mOffline"
      }\x1b[0m\r\n\r\n`;
      welcomeMessage +=
        "\x1b[37mType commands below or use the chat on the left for help.\x1b[0m\r\n\r\n";

      this.term.write(welcomeMessage);
    } catch (error) {
      console.error("Failed to display welcome message:", error);
      this.term.write(
        "\r\n\x1b[33mWelcome to BanditGUI Terminal\x1b[0m\r\n\r\n"
      );
    }
  }

  /**
   * Initialize the chat interface
   */
  initChat() {
    this.chatInput = document.getElementById("chat-input");
    this.chatSubmit = document.getElementById("chat-submit");
    this.chatMessages = document.getElementById("chat-messages");
    this.llmDropdown = document.getElementById("llm-selection-dropdown");
    this.askAProButton = document.getElementById("ask-a-pro-button");

    this.populateLlmDropdown();
  }

  /**
   * Populate the LLM selection dropdown
   */
  async populateLlmDropdown() {
    try {
      const response = await fetch("/config/llm_model.json");
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const llmModels = await response.json();

      for (const provider in llmModels) {
        if (llmModels.hasOwnProperty(provider)) {
          if (provider === "ollama") {
            // Fetch dynamic Ollama models from local API
            try {
              // Use a configurable Ollama API endpoint, defaulting to localhost if not set
              const ollamaApiUrl =
                window.OLLAMA_API_URL ||
                document.body.dataset.ollamaApiUrl ||
                "http://localhost:11434/api/tags";
              const ollamaResp = await fetch(ollamaApiUrl);
              let ollamaData;
              if (ollamaResp.ok) {
                ollamaData = await ollamaResp.json();
                if (ollamaData.models && Array.isArray(ollamaData.models)) {
                  ollamaData.models.forEach((modelObj) => {
                    if (modelObj && modelObj.name) {
                      // Check for presence of 'name'
                      const modelName = modelObj.name;
                      const option = document.createElement("option");
                      option.value = `${provider}/${modelName}`;
                      option.textContent = `Ollama: ${modelName}`;
                      this.llmDropdown.appendChild(option);
                    }
                  });
                }
              } else {
                let errorMsg = "Could not fetch Ollama models";
                errorMsg += ` (HTTP status: ${ollamaResp.status})`;
                try {
                  ollamaData = await ollamaResp.json();
                  if (ollamaData && ollamaData.error) {
                    errorMsg += ` - Response: ${JSON.stringify(ollamaData)}`;
                  }
                } catch (jsonErr) {
                  // Ignore JSON parse errors
                }
                throw new Error(errorMsg);
              }
            } catch (ollamaErr) {
              console.error("Error fetching Ollama models:", ollamaErr);
              // Fallback to static list if Ollama API fails
              llmModels[provider].forEach((modelName) => {
                const option = document.createElement("option");
                option.value = `${provider}/${modelName}`;
                option.textContent = `Ollama: ${modelName}`;
                this.llmDropdown.appendChild(option);
              });
            }
          } else {
            llmModels[provider].forEach((modelName) => {
              const option = document.createElement("option");
              option.value = `${provider}/${modelName}`;
              const providerDisplay =
                provider.charAt(0).toUpperCase() + provider.slice(1);
              option.textContent = `${providerDisplay}: ${modelName}`;
              this.llmDropdown.appendChild(option);
            });
          }
        }
      }
    } catch (error) {
      console.error("Error fetching or parsing LLM models:", error);
      const option = document.createElement("option");
      option.value = "";
      option.textContent = "Error loading models";
      this.llmDropdown.appendChild(option);
    }
  }

  /**
   * Initialize the terminal interface
   */
  initTerminal() {
    // Create terminal
    this.term = new Terminal({
      cursorBlink: true,
      theme: {
        background: "#121212",
        foreground: "#e0e0e0",
        cursor: "#38b6ff",
        selection: "rgba(58, 134, 255, 0.3)",
        black: "#2d2d2d",
        red: "#ff5c8d",
        green: "#38b000",
        yellow: "#ff9f1c",
        blue: "#3a86ff",
        magenta: "#9d4edd",
        cyan: "#38b6ff",
        white: "#e0e0e0",
        brightBlack: "#6c6c6c",
        brightRed: "#ff5c8d",
        brightGreen: "#38b000",
        brightYellow: "#ff9f1c",
        brightBlue: "#3a86ff",
        brightMagenta: "#9d4edd",
        brightCyan: "#38b6ff",
        brightWhite: "#ffffff",
      },
      fontFamily:
        '"Fira Code", "Cascadia Code", "Source Code Pro", Consolas, "DejaVu Sans Mono", monospace',
      fontSize: 14,
      lineHeight: 1.2,
      scrollback: 1000,
      convertEol: true,
      padding: 20, // Increased padding to fix text being cut off
    });

    // Load addons
    this.fitAddon = new FitAddon.FitAddon();
    this.term.loadAddon(this.fitAddon);
    this.term.loadAddon(new WebLinksAddon.WebLinksAddon());

    // Open terminal in container
    this.term.open(document.getElementById("terminal-container"));
    this.fitAddon.fit();

    // Handle pasted data - write directly and add to command
    this.term.onData((data) => {
      // Only handle paste data, not regular keystrokes (which go through onKey)
      if (data.length > 1) {
        this.currentCommand += data;
        this.term.write(data);
      }
    });

    // Terminal state
    this.history = [];
    this.historyIndex = -1;
    this.currentCommand = "";
    this.prompt = "$ ";

    // Set up terminal key event handling
    this.term.onKey(this.handleTerminalKeyEvent.bind(this));

    // Display welcome message and then write prompt
    this.displayTerminalWelcome().then(() => {
      this.writePrompt();
    });

    // Update connection status UI
    this.updateConnectionStatus();
  }

  /**
   * Set up event listeners for UI elements
   */
  setupEventListeners() {
    // Chat input submission
    this.chatInput.addEventListener("keydown", (e) => {
      if (e.key === "Enter") {
        this.handleChatSubmit();
      }
    });

    this.chatSubmit.addEventListener("click", () => {
      this.handleChatSubmit();
    });

    // Ask a Pro button event listener
    if (this.askAProButton) {
      this.askAProButton.addEventListener("click", async () => {
        const selectedLlm = this.llmDropdown.value;
        const levelName = this.currentLevel;
        const levelDescription = this.currentLevelDescription;
        const commandHistory = this.history;

        if (levelName === null || !levelDescription) {
          this.addAssistantMessage(
            "Please make sure you have started a level and its information is displayed before using Ask-a-Pro. Try using the 'level' or 'start' command in the chat."
          );
          return;
        }

        const escapeHTML = (str) =>
          str == null
            ? ""
            : String(str)
                .replace(/&/g, "&amp;")
                .replace(/</g, "&lt;")
                .replace(/>/g, "&gt;")
                .replace(/"/g, "&quot;")
                .replace(/'/g, "&#039;");
        const safeLevelDescription = escapeHTML(levelDescription);
        const safeCommandHistoryString =
          commandHistory.length > 0
            ? commandHistory.map((cmd) => escapeHTML(cmd)).join(", ")
            : "empty";
        this.addUserMessage(
          `Asking the Pro about Level ${levelName}: '${safeLevelDescription}' (Command history: ${safeCommandHistoryString})`
        );
        this.addMentorMessage(
          `Thinking like a Pro with ${selectedLlm.split("/")[1]}...`
        );

        try {
          const response = await fetch("/ask-a-pro", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              llm: selectedLlm,
              level_name: levelName,
              level_description: levelDescription,
              command_history: commandHistory,
            }),
          });

          const data = await response.json();

          if (response.ok && data.status === "success") {
            this.addMentorMessage(data.advice);
          } else {
            this.addAssistantMessage(
              `Sorry, I couldn't get advice from the Pro. ${
                data.message || "Unknown server error."
              }`
            );
          }
        } catch (error) {
          console.error("Error calling Ask-a-Pro:", error);
          this.addAssistantMessage(
            `Sorry, I couldn't get advice from the Pro. Error: ${error.message}`
          );
        }
      });
    }

    // Start game button
    const startGameButton = document.getElementById("start-game-button");
    if (startGameButton) {
      startGameButton.addEventListener("click", () => {
        this.startNewGame();
        // Ensure the chat input is visible
        this.chatInput.style.display = "block";
      });
    }

    // Handle terminal resize to ensure proper fit
    window.addEventListener("resize", () => {
      this.fitAddon.fit();
    });
  }

  /**
   * Handle chat input submission
   */
  handleChatSubmit() {
    const message = this.chatInput.value.trim();
    if (!message) return;

    // Clear the input
    this.chatInput.value = "";

    // Display user message
    this.addUserMessage(message);

    // Process the message
    this.processChatCommand(message);
  }

  /**
   * Process chat messages
   */
  async processChatCommand(message) {
    // Split the message into command and arguments
    const parts = message.toLowerCase().trim().split(/\s+/);
    const command = parts[0];

    // Check for special commands
    if (command === "help") {
      this.showHelpInfo();
    } else if (command === "info") {
      this.showConnectionInfo();
    } else if (command === "level") {
      // Check if a level number was provided
      if (parts.length > 1 && !isNaN(parseInt(parts[1]))) {
        const levelNum = parseInt(parts[1]);
        await this.showLevelInfo(levelNum);
      } else {
        await this.showLevelInfo();
      }
    } else if (command === "hint") {
      // Show hints for the current level
      await this.showLevelHints();
    } else if (command === "start") {
      // Start a new game
      await this.startNewGame();
    } else if (command === "clear") {
      // Clear the chat messages
      this.chatMessages.innerHTML = "";
      // Add a system message
      this.addAssistantMessage("Chat cleared.");
    } else if (command === "quit" || command === "exit") {
      this.addAssistantMessage(
        "To exit the application, simply close the browser tab or window."
      );
    } else {
      // For any other message, treat as a general question
      this.addAssistantMessage(
        "I'm a simple assistant for the Bandit wargame. I can help with basic commands like 'help', 'info', 'level', 'hint', 'start', and 'clear'. For game interaction, please use the terminal on the right."
      );
    }
  }

  /**
   * Show help information
   */
  showHelpInfo() {
    const helpMessage = `
<strong>OverTheWire Bandit Wargame</strong>

Bandit is a beginner-friendly wargame designed to teach the basics of Linux command line, security concepts, and common tools used in cybersecurity.

<strong>How to Play:</strong>
1. Type <code>start</code> or click the <code>Start</code> button to display Level 0 instructions
2. Connect to the Bandit server using the terminal on the right
3. Each level requires you to find a password to access the next level
4. Use Linux commands to navigate the system and find the password
5. Use the password to log in to the next level

<strong>Available Commands in Chat:</strong>
- <code>start</code> - Start a new game
- <code>help</code> - Display this help information
- <code>info</code> - Show connection status and current level
- <code>level</code> - Display instructions for the current level
- <code>level [number]</code> - Display instructions for a specific level
- <code>hint</code> - Get hints for the current level
- <code>clear</code> - Clear the chat messages
- <code>quit</code> - Exit information

<strong>Terminal Commands:</strong>
Use the terminal to execute SSH and Linux commands directly on the server.

<strong>Useful Linux Commands:</strong>
- <code>ls</code> - List files in the current directory
- <code>cd</code> - Change directory
- <code>cat</code> - Display file contents
- <code>file</code> - Determine file type
- <code>find</code> - Search for files
- <code>grep</code> - Search for patterns in files

For more information, visit <a href="https://overthewire.org/wargames/bandit/" target="_blank">OverTheWire Bandit</a>
`;
    this.addAssistantMessage(helpMessage);
  }

  /**
   * Show connection information
   */
  showConnectionInfo() {
    let infoMessage;

    if (this.isConnected) {
      infoMessage = `
<strong>Connection Status:</strong> Connected to Bandit server
<strong>Current Level:</strong> ${this.currentLevel || "Unknown"}
<strong>Server:</strong> bandit.labs.overthewire.org:2220

Use the terminal on the right to execute commands on the server.
Type <code>level</code> in this chat to see the current level instructions.
`;
    } else {
      infoMessage = `
<strong>Connection Status:</strong> Not connected to Bandit server
<strong>Server:</strong> bandit.labs.overthewire.org:2220
<strong>Server Status:</strong> ${
        this.serverStatus === "online"
          ? '<span style="color: #38b000;">Online</span>'
          : this.serverStatus === "offline"
          ? '<span style="color: #ff5c8d;">Offline</span>'
          : '<span style="color: #ff9f1c;">Unknown</span>'
      }

<p>"The journey of a thousand miles begins with a single connection." ~Confucius (if he was a programmer)</p>

<p>Type <code>level</code> in the chat to get instructions for the current level.</p>
`;
    }

    this.addAssistantMessage(infoMessage);
  }

  /**
   * Show level information
   * @param {number|null} specificLevel - Optional specific level to show
   */
  async showLevelInfo(specificLevel = null) {
    // Level information is stored locally, so we don't need to be connected
    // Use the specified level, or current level, or default to 0
    const levelToShow =
      specificLevel !== null
        ? specificLevel
        : this.isConnected
        ? this.currentLevel || 0
        : 0;

    try {
      // Get the level information from the server
      const response = await fetch("/level-info", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRF-Token": sessionStorage.getItem("csrfToken"),
        },
        body: JSON.stringify({ level: levelToShow }),
      });

      const data = await response.json();

      if (data.status === "success") {
        const levelInfo = data.levelInfo;
        this.currentLevelDescription = levelInfo.goal; // Store for Ask-a-Pro

        // Format the commands with links if available
        let commandsHtml = "";
        if (levelInfo.commands) {
          const commandsList = levelInfo.commands
            .split(",")
            .map((cmd) => cmd.trim());
          const commandsLinks = levelInfo.commands_links || [];

          commandsHtml = commandsList
            .map((cmd) => {
              // Find a matching link for this command
              const linkObj = commandsLinks.find((link) => link.text === cmd);
              if (linkObj) {
                return `<a href="${linkObj.url}" target="_blank">${cmd}</a>`;
              }
              return cmd;
            })
            .join(", ");
        }

        // Format the reading material with links if available
        let readingHtml = "";
        if (levelInfo.reading) {
          const readingList = levelInfo.reading
            .split(",")
            .map((item) => item.trim());
          const readingLinks = levelInfo.reading_links || [];

          readingHtml = readingLinks
            .map((link) => {
              return `<a href="${link.url}" target="_blank">${link.text}</a>`;
            })
            .join("<br>");
        }

        let levelMessage = `
<div class="level-info">
<h4>Level ${levelInfo.level}</h4>

<strong>Goal:</strong>
<p>${levelInfo.goal}</p>

<strong>Commands you may need:</strong>
<p>${commandsHtml || levelInfo.commands}</p>

<strong>Helpful reading material:</strong>
<p>${readingHtml || levelInfo.reading || "None provided"}</p>

<strong>Connection Command:</strong>
<pre>ssh bandit${levelInfo.level}@bandit.labs.overthewire.org -p 2220</pre>

<p class="hint-prompt">Type <code>hint</code> in the chat if you need additional help.</p>
</div>
`;
        this.addAssistantMessage(levelMessage);
      } else {
        this.addAssistantMessage(
          `Error getting level information: ${data.message}`
        );
      }
    } catch (error) {
      this.addAssistantMessage(
        `Error: Failed to get level information. ${error.message}`
      );
      console.error("Level info error:", error);
    }
  }

  /**
   * Show hints for the current level
   */
  async showLevelHints() {
    if (!this.currentLevel && this.currentLevel !== 0) {
      this.addAssistantMessage(
        "Please display Level 0 instructions first by typing <code>start</code> in the chat or clicking the <code>Start</code> button."
      );
      return;
    }

    try {
      // Get the level information from the server
      const response = await fetch("/level-info", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ level: this.currentLevel || 0 }),
      });

      const data = await response.json();

      if (data.status === "success") {
        const levelInfo = data.levelInfo;

        // Create a hint message based on the level
        let hintMessage = `<div class="level-hint">
<h4>Hints for Level ${levelInfo.level}</h4>
`;

        // Add level-specific hints
        switch (parseInt(levelInfo.level)) {
          case 0:
            hintMessage += `<p>For this level, you just need to log in. Use the SSH command shown and the password 'bandit0'.</p>
<p>Once logged in, you can use <code>ls</code> to list files and <code>cat</code> to read file contents.</p>`;
            break;
          case 1:
            hintMessage += `<p>The password is stored in a file called 'readme' in the home directory.</p>
<p>Use <code>ls</code> to see the file and <code>cat readme</code> to read its contents.</p>`;
            break;
          case 2:
            hintMessage += `<p>The password is stored in a file called '-' which is a special character in Linux.</p>
<p>To read files with special characters in their names, you can use <code>cat ./-</code> or <code>cat < -</code>.</p>`;
            break;
          default:
            hintMessage += `<p>For this level, carefully read the goal and think about which commands might help.</p>
<p>Remember to use <code>man [command]</code> to read the manual for any command you're not familiar with.</p>`;
        }

        hintMessage += `</div>`;

        this.addAssistantMessage(hintMessage);
      } else {
        this.addAssistantMessage(
          `Error getting hint information: ${data.message}`
        );
      }
    } catch (error) {
      this.addAssistantMessage(
        `Error: Failed to get hint information. ${error.message}`
      );
      console.error("Hint info error:", error);
    }
  }

  /**
   * Start a new game
   */
  async startNewGame() {
    // Clear the chat messages
    this.chatMessages.innerHTML = "";
    // Add an improved welcome message with Level 0 directive
    const welcomeMessage = `<div class="system-message">
            <h3>🎯 Welcome to BanditGUI!</h3>
            <p><strong>Level 0 - Your First Challenge</strong></p>
            <div style="background-color: #2a2a2a; padding: 15px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #00ff00;">
                <h4>🎮 Mission Directive:</h4>
                <p>Your goal is to log into the Bandit game using SSH (Secure Shell).</p>
                <p><strong>Connection Details:</strong></p>
                <ul>
                    <li><strong>Host:</strong> bandit.labs.overthewire.org</li>
                    <li><strong>Port:</strong> 2220</li>
                    <li><strong>Username:</strong> bandit0</li>
                    <li><strong>Password:</strong> bandit0</li>
                </ul>
                <p><strong>SSH Command:</strong></p>
                <code style="background-color: #1a1a1a; padding: 5px; border-radius: 3px; display: block; margin: 5px 0;">
                    ssh <EMAIL> -p 2220
                </code>
                <p>💡 <em>Once connected, you'll be ready to start your journey through the Bandit challenges!</em></p>
            </div>
            <p>Use the terminal on the right to execute commands, or ask me for help anytime! 🚀</p>
        </div>`;
    this.chatMessages.innerHTML = welcomeMessage;
    this.currentLevel = 0;
    // Update connection status UI
    this.updateConnectionStatus();
    // Start a new session in the backend
    const sessionId = this.getSessionId();
    try {
      const csrfToken = sessionStorage.getItem("csrfToken");
      await fetch("/progress/session", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRF-Token": csrfToken,
        },
        body: JSON.stringify({ session_id: sessionId }),
      });
    } catch (e) {
      console.error("Failed to start session:", e);
      this.addAssistantMessage("Failed to start new game. Please try again.");
    }
  }

  /**
   * Handle quit command
   */
  async handleQuit() {
    if (this.isConnected) {
      await this.disconnectFromServer();
    }

    this.addAssistantMessage(
      "Goodbye! The application will close in a few seconds..."
    );

    // Simulate application exit after a short delay
    setTimeout(() => {
      this.addAssistantMessage(
        "To actually exit the application, close this browser tab or window."
      );
    }, 3000);
  }

  /**
   * Add a user message to the chat
   */
  addUserMessage(message) {
    const messageElement = document.createElement("div");
    messageElement.className = "user-message";
    messageElement.innerHTML = `
            <div class="user-label">You:</div>
            <div class="message-content">${message}</div>
        `;
    this.chatMessages.appendChild(messageElement);
    this.scrollChatToBottom();
  }

  /**
   * Add a mentor message to the chat
   */
  addMentorMessage(message) {
    const messageElement = document.createElement("div");
    messageElement.className = "mentor-message";
    // Using Font Awesome icon
    messageElement.innerHTML = `
            <div class="mentor-label"><i class="fas fa-user-graduate"></i> Mentor:</div>
            <div class="message-content"></div>
        `;
    const messageContent = messageElement.querySelector(".message-content");
    // Use marked.parse to render Markdown content
    messageContent.innerHTML = marked.parse(message);
    this.chatMessages.appendChild(messageElement);
    this.scrollChatToBottom();
  }

  /**
   * Add an assistant message to the chat
   */
  addAssistantMessage(message) {
    const messageElement = document.createElement("div");
    messageElement.className = "assistant-message";

    // Format the message with proper line breaks
    const formattedMessage = message
      .replace(/\n\n/g, "</p><p>") // Double line breaks become new paragraphs
      .replace(/\n/g, "<br>"); // Single line breaks become <br>

    messageElement.innerHTML = `
            <div class="assistant-label">Assistant:</div>
            <div class="message-content"><p>${formattedMessage}</p></div>
        `;
    this.chatMessages.appendChild(messageElement);
    this.scrollChatToBottom();
  }

  /**
   * Scroll chat to the bottom
   */
  scrollChatToBottom() {
    this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
  }

  /**
   * Handle terminal key events
   */
  handleTerminalKeyEvent(e) {
    const ev = e.domEvent;
    const printable = !ev.altKey && !ev.ctrlKey && !ev.metaKey;

    if (ev.keyCode === 13) {
      // Enter key
      this.handleTerminalEnter();
    } else if (ev.keyCode === 8) {
      // Backspace
      if (this.currentCommand.length > 0) {
        this.currentCommand = this.currentCommand.slice(0, -1);
        this.term.write("\b \b");
      }
    } else if (ev.keyCode === 38) {
      // Up arrow
      this.navigateHistory("up");
    } else if (ev.keyCode === 40) {
      // Down arrow
      this.navigateHistory("down");
    } else if (printable) {
      this.currentCommand += e.key;
      this.term.write(e.key);
    }
  }

  /**
   * Handle terminal Enter key
   */
  async handleTerminalEnter() {
    this.term.write("\r\n");

    const command = this.currentCommand.trim();
    if (command) {
      this.history.push(command);
      this.historyIndex = this.history.length;

      // Execute all commands directly
      await this.executeCommand(command);
    }

    this.currentCommand = "";
    this.writePrompt();
  }

  /**
   * Navigate command history
   */
  navigateHistory(direction) {
    if (direction === "up" && this.historyIndex > 0) {
      this.historyIndex--;
    } else if (
      direction === "down" &&
      this.historyIndex < this.history.length
    ) {
      this.historyIndex++;
    } else {
      return;
    }

    // Clear current command
    this.term.write(
      "\r" + " ".repeat(this.prompt.length + this.currentCommand.length) + "\r"
    );

    // Write new command from history or empty if at the end
    const newCommand =
      this.historyIndex < this.history.length
        ? this.history[this.historyIndex]
        : "";
    this.currentCommand = newCommand;
    this.term.write(this.prompt + newCommand);
  }

  /**
   * Execute a command on the server
   */
  async executeCommand(command, options = {}) {
    try {
      const csrfToken = sessionStorage.getItem("csrfToken");
      if (!csrfToken) {
        throw new Error("No CSRF token available");
      }

      const payload = { command };
      if (options.username) payload.username = options.username;
      if (options.password) payload.password = options.password;

      const response = await fetch("/execute", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRF-Token": csrfToken,
        },
        body: JSON.stringify(payload),
      });
      const data = await response.json();

      // Show command output
      if (data.output) {
        this.writeLine(data.output);
      }

      // Update CSRF token if provided in response
      if (data.csrfToken) {
        sessionStorage.setItem("csrfToken", data.csrfToken);
      }

      // Check if the level has changed
      if (
        data.currentLevel !== undefined &&
        data.currentLevel !== this.currentLevel
      ) {
        this.currentLevel = data.currentLevel;
        this.term.write(
          `\r\n\x1b[33mYou are now on level ${this.currentLevel}\x1b[0m\r\n`
        );
      }

      return data;
    } catch (error) {
      console.error("Error executing command:", error);
      throw error;
    }
  }

  async connectToServer() {
    try {
      const csrfToken = sessionStorage.getItem("csrfToken");
      if (!csrfToken) {
        throw new Error("No CSRF token available");
      }

      const response = await fetch("/connect", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRF-Token": csrfToken,
        },
      });
      const data = await response.json();

      if (data.csrfToken) {
        sessionStorage.setItem("csrfToken", data.csrfToken);
      }
      this.isConnected = true;
      this.currentLevel = data.currentLevel || 0;
      this.updateConnectionStatus();
      this.displayTerminalWelcome();
      return data;
    } catch (error) {
      // Handle rate limit errors
      if (error.status === 429) {
        this.writeLine(
          `\r\n\x1b[31mError: Too many connection attempts. Please wait a moment and try again.\x1b[0m\r\n`
        );
        return { status: "error", message: "Rate limit exceeded" };
      }

      // Handle CSRF errors
      if (error.status === 403) {
        this.writeLine(
          `\r\n\x1b[31mError: Invalid CSRF token. Please refresh the page and try again.\x1b[0m\r\n`
        );
        return { status: "error", message: "Invalid CSRF token" };
      }

      this.writeLine(
        `\r\n\x1b[31mConnection failed: ${error.message}\x1b[0m\r\n`
      );
      this.addAssistantMessage(`Connection failed: ${error.message}`);
      return { status: "error", message: error.message };
    }
  }

  async disconnectFromServer() {
    try {
      const csrfToken = sessionStorage.getItem("csrfToken");
      if (!csrfToken) {
        throw new Error("No CSRF token available");
      }

      const response = await fetch("/disconnect", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRF-Token": csrfToken,
        },
      });
      const data = await response.json();

      if (data.status === "success") {
        // Update CSRF token if provided
        if (data.csrfToken) {
          sessionStorage.setItem("csrfToken", data.csrfToken);
        }
        this.isConnected = false;
        this.currentLevel = null;
        this.updateConnectionStatus();
        this.term.write("\r\n\x1b[33mDisconnected from SSH server.\x1b[0m\r\n");

        // Update the connect button text
        const connectButton = document.getElementById("connect-button");
        connectButton.textContent = "Connect to Server";
        connectButton.classList.remove("disconnect");

        // Show a message in the chat
        this.addAssistantMessage("Disconnected from the Bandit server.");
      } else {
        this.term.write(
          `\r\n\x1b[31mFailed to disconnect: ${data.message}\x1b[0m\r\n`
        );
        this.addAssistantMessage(
          `Failed to disconnect from the server: ${data.message}`
        );
      }
    } catch (error) {
      // Handle rate limit errors
      if (error.status === 429) {
        this.writeLine(
          `\r\n\x1b[31mError: Too many disconnection attempts. Please wait a moment and try again.\x1b[0m\r\n`
        );
        return { status: "error", message: "Rate limit exceeded" };
      }

      // Handle CSRF errors
      if (error.status === 403) {
        this.writeLine(
          `\r\n\x1b[31mError: Invalid CSRF token. Please refresh the page and try again.\x1b[0m\r\n`
        );
        return { status: "error", message: "Invalid CSRF token" };
      }

      this.writeLine(
        `\r\n\x1b[31mError: Failed to disconnect. ${error.message}\x1b[0m\r\n`
      );
      this.addAssistantMessage(
        `Error disconnecting from the server: ${error.message}`
      );
      console.error("Disconnection error:", error);
    }
  }

  /**
   * Save the current level and found passwords to local storage
   */
  saveProgress() {
    const progress = {
      currentLevel: this.currentLevel,
      foundPasswords: this.foundPasswords,
    };
    localStorage.setItem("banditProgress", JSON.stringify(progress));
  }

  /**
   * Load the current level and found passwords from local storage
   */
  loadProgress() {
    const progress = JSON.parse(localStorage.getItem("banditProgress"));
    if (progress) {
      this.currentLevel = progress.currentLevel;
      this.foundPasswords = progress.foundPasswords;
    }
  }

  /**
   * Show the resume modal for a given level
   */
  showResumeModal(level) {
    // Create modal elements
    const modal = document.createElement("div");
    modal.className = "resume-modal";
    modal.style =
      "position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: #222; color: #fff; padding: 2em; border-radius: 8px; z-index: 10000; min-width: 300px; box-shadow: 0 2px 16px #0008;";

    // Modal content
    const modalContent = document.createElement("div");
    modalContent.style = "padding: 1em;";

    // Password field
    const pwField = document.createElement("input");
    pwField.type = "password";
    pwField.style = "width: 100%; margin: 0.5em 0; padding: 0.5em;";
    pwField.value = this.foundPasswords[level] || "";
    modalContent.appendChild(pwField);

    // Reveal button
    let revealed = false;
    const revealBtn = document.createElement("button");
    revealBtn.textContent = "Show";
    revealBtn.style = "margin-top: 0.5em; padding: 0.5em; margin-right: 0.5em;";
    revealBtn.onclick = () => {
      revealed = !revealed;
      pwField.type = revealed ? "text" : "password";
      revealBtn.textContent = revealed ? "Hide" : "Show";
    };
    modalContent.appendChild(revealBtn);

    // Resume button
    const resumeBtn = document.createElement("button");
    resumeBtn.textContent = "Resume Level";
    resumeBtn.style = "margin-top: 0.5em; padding: 0.5em; margin-right: 0.5em;";
    resumeBtn.onclick = async () => {
      try {
        document.body.removeChild(modal);
        await this.showLevelInfo(Number(level));
        await this.connectToNextLevel(
          Number(level),
          this.foundPasswords[level]
        );
      } catch (error) {
        console.error("Error resuming level:", error);
        this.writeLine(
          `\r\n\x1b[31mError: Failed to resume level. ${error.message}\x1b[0m\r\n`
        );
        this.addAssistantMessage(`Error resuming level: ${error.message}`);
      }
    };
    modalContent.appendChild(resumeBtn);

    // Delete button - Remove password from local storage
    const deleteBtn = document.createElement("button");
    deleteBtn.textContent = "Delete Password";
    deleteBtn.style =
      "margin-top: 0.5em; padding: 0.5em; background: #b00; color: #fff;";
    deleteBtn.onclick = () => {
      try {
        delete this.foundPasswords[level];
        localStorage.setItem(
          "banditProgress",
          JSON.stringify({
            currentLevel: this.currentLevel,
            foundPasswords: this.foundPasswords,
          })
        );
        this.writeLine(
          `\r\n\x1b[32mPassword removed successfully from local storage.\x1b[0m\r\n`
        );
        document.body.removeChild(modal);
      } catch (e) {
        this.writeLine(
          `\r\n\x1b[31mError: Failed to remove password. ${e.message}\x1b[0m\r\n`
        );
        console.error("Error removing password:", e);
      }
    };
    modalContent.appendChild(deleteBtn);

    // Close button
    const closeBtn = document.createElement("button");
    closeBtn.textContent = "Close";
    closeBtn.style =
      "margin-top:1.5em; background:#333; color:#bada55; border:none; border-radius:4px; padding:0.5em 1.5em; cursor:pointer; float:right;";
    closeBtn.onclick = () => {
      try {
        document.body.removeChild(modal);
      } catch (error) {
        console.error("Error in modal onclick:", error);
        throw error;
      }
    };
    modalContent.appendChild(closeBtn);

    modal.appendChild(modalContent);
    document.body.appendChild(modal);
  }
}

// ...

// Initialize the application when the page loads
document.addEventListener("DOMContentLoaded", () => {
  window.banditApp = new BanditApp();
});
