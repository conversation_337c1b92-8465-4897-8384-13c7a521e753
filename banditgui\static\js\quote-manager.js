/**
 * Quote Manager for BanditGUI
 *
 * This script handles loading and displaying geek pop culture quotes.
 */

class QuoteManager {
    constructor() {
        this.quotes = [];
        this.welcomeQuotes = [];
        this.currentQuoteIndex = 0;
    }

    /**
     * Initialize the quote manager
     */
    async init() {
        try {
            // Load welcome quotes
            await this.loadWelcomeQuotes();
            // Quote rotation removed - only keeping terminal welcome quote
        } catch (error) {
            console.error('Error initializing quote manager:', error);
        }
    }

    /**
     * Load welcome quotes for terminal
     */
    async loadWelcomeQuotes(count = 1) {
        try {
            const response = await fetch(`/quotes/welcome?count=${count}`);
            const data = await response.json();

            if (data.status === 'success') {
                this.welcomeQuotes = data.quotes;
                return this.welcomeQuotes;
            } else {
                console.error('Error loading welcome quotes:', data.message);
                return this.getDefaultWelcomeQuotes();
            }
        } catch (error) {
            console.error('Error fetching welcome quotes:', error);
            return this.getDefaultWelcomeQuotes();
        }
    }

    /**
     * Get default welcome quotes if API fails
     */
    getDefaultWelcomeQuotes() {
        return [
            '"Have you tried turning it off and on again?"'
        ];
    }

    /**
     */
    getDefaultQuote() {
        return {
            text: "The code must flow.",
            source: "Dune (modified)",
            character: "Paul Atreides as a programmer"
        };
    }

    /**
     * Format a quote object into a string
     */
    formatQuote(quote) {
        return `"${quote.text}"`;
    }

    /**
     * Get welcome quotes for terminal
     */
    getWelcomeQuotes() {
        return this.welcomeQuotes.length > 0 ? this.welcomeQuotes : this.getDefaultWelcomeQuotes();
    }


}

// Initialize Quote Manager when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.quoteManager = new QuoteManager();
    window.quoteManager.init();
});
