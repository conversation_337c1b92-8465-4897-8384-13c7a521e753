/* Custom styles for xterm.js terminal */
.terminal-container {
    width: 100%;
    height: 100%;
    padding: 1rem;
    background-color: var(--primary-dark); /* Updated background color */
    border-radius: 0 0 0.375rem 0.375rem; /* Updated border-radius */
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
    margin: 0.3125rem;
}

.xterm {
    font-feature-settings: "liga" 0;
    position: relative;
    user-select: none;
    -ms-user-select: none;
    -webkit-user-select: none;
}

.xterm.focus,
.xterm:focus {
    outline: none;
}

.xterm .xterm-helpers {
    position: absolute;
    top: 0;
    z-index: 5;
}

.xterm .xterm-helper-textarea {
    position: absolute;
    opacity: 0;
    left: -9999em;
    top: 0;
    width: 0;
    height: 0;
    z-index: -5;
    white-space: nowrap;
    overflow: hidden;
    resize: none;
}

.xterm .composition-view {
    background: #000;
    color: #FFF;
    display: none;
    position: absolute;
    white-space: nowrap;
    z-index: 1;
}

.xterm .xterm-viewport {
    background-color: var(--primary-dark); /* Updated background color */
    overflow-y: scroll;
    cursor: default;
    position: absolute;
    right: 0;
    left: 0;
    top: 0;
    bottom: 0;
    border-radius: 0 0 6px 6px; /* Updated border-radius */
}

.xterm .xterm-screen {
    position: relative;
}

.xterm .xterm-screen canvas {
    position: absolute;
    left: 0.3125rem;
    top: 0.3125rem;
}

.xterm-cursor-layer {
    z-index: 3;
}

.xterm-cursor {
    background-color: var(--accent-primary); /* Updated background color */
color: var(--text-primary); /* Or a specific variable like --text-on-accent-strong if #FFFFFF is required */
    border-radius: 1px;
}

.xterm-cursor.xterm-cursor-blink {
    animation: xterm-cursor-blink 1.2s infinite step-end;
}

@keyframes xterm-cursor-blink {
    0% {
        background-color: var(--accent-primary); /* Updated background color */
color: var(--text-primary); /* Or a specific variable like --text-on-accent-strong if #FFFFFF is required */
    }
    50% {
        background-color: transparent;
        color: inherit;
    }
}

.xterm-selection {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    opacity: 0.3;
    pointer-events: none;
}

.xterm-selection div {
    position: absolute;
background-color: color-mix(in srgb, var(--accent-primary) 40%, transparent); /* Updated background color */
}

/* Ensure the terminal container fills the panel */
.panel.right {
    display: flex;
    flex-direction: column;
}

.panel.right h3 {
    margin-top: 0;
    margin-bottom: 10px;
}

#terminal-container {
    flex: 1;
    min-height: clamp(18.75rem, 40vh, 31.25rem);
    border-radius: 0 0 0.375rem 0.375rem;
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
    margin: 0;
    padding: 1rem;
}
