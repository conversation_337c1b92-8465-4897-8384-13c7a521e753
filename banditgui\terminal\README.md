# BanditGUI Terminal Module

This module provides terminal functionality for the BanditGUI application, specifically designed to interact with the OverTheWire Bandit wargame server.

## Features

- Terminal command execution and management
- SSH connection handling for Bandit wargame levels
- Level information management
- Built-in commands for help, info, and level management
- Comprehensive logging with multiple log types

## Available Commands

The terminal module supports several built-in commands:

- `help`: Display help information about available commands
- `info`: Show information about the Bandit server
- `clear`: Clear the terminal screen
- `level [number]`: Display information about a specific level
- `general`: Display general information about the Bandit wargame
- `ssh [options]`: Connect to the SSH server

## Logging Configuration

The terminal module integrates with the application's logging system, which includes:
- Separate log files for different log types (security, audit, API, error)
- Custom formatters for better structured logging
- Detailed security and audit logging
- Error tracking with traceback information

## Usage

The terminal module is initialized with an optional SSH manager for handling SSH connections. It maintains state about the current connection status and level information.

### Example Usage

```python
# Initialize terminal manager
terminal = TerminalManager(ssh_manager=ssh_manager)

# Execute a command
output = terminal.execute_command("ls -la")

# Get help information
help_text = terminal.execute_command("help")
```

## SSH Connection Handling

The module includes special handling for SSH commands:
- Automatically detects and extracts level information from SSH connections
- Maintains connection state
- Provides helpful error messages when not connected

## Error Handling

All operations are logged with appropriate error handling, and the module provides user-friendly error messages while maintaining detailed logging for debugging purposes.

## Security Features

- Secure handling of SSH credentials
- Level detection from command output
- Comprehensive logging for security and audit purposes

## Dependencies

- Requires the `logging_config` module for logging functionality
- Depends on `settings` for configuration values
- Utilizes utility functions from the `utils` module

## Notes

This module is part of the BanditGUI application and is designed to work with the OverTheWire Bandit wargame. It provides a command-line interface for interacting with the game's levels through SSH connections.
