# BanditGUI Utilities

This directory contains utility modules that provide essential functionality for the BanditGUI application. These utilities are organized into several key categories:

## Core Utilities

### Level Information
- `level_info.py`: Manages level data and caching for the Bandit wargame
- `get_data.py`: Handles data retrieval and processing
- `json_schema.py`: Provides JSON schema validation

### Security & Authentication
- `csrf.py`: Implements CSRF protection
- `session_manager.py`: Manages user sessions
- `api_key_manager.py`: Handles API key generation and validation
- `security_logger.py`: Provides specialized security logging

### Rate Limiting & Progression
- `rate_limiter.py`: Implements rate limiting to prevent abuse
- `progression_manager.py`: Manages user progression through levels
- `quotes.py`: Provides quote management utilities

### Validation & Testing
- `validation.py`: General data validation utilities
- `password_detection.py`: Password-related validation
- `test_*`: Various test files for utility modules

## Key Features

### Logging System
- Comprehensive logging configuration
- Separate log files for different log types (security, audit, API, error)
- Custom formatters for better log readability
- Detailed security and audit logging
- Error tracking with traceback information

### Security Features
- CSRF protection
- Session management
- API key validation
- Rate limiting
- Security-specific logging

### Data Management
- Efficient caching mechanisms
- JSON schema validation
- Level data management
- Progression tracking

## Usage

### Level Information
```python
from banditgui.utils import level_info

# Get general game information
info = level_info.get_general_info()

# Get available levels
levels = level_info.get_available_levels()

# Get specific level information
level_data = level_info.get_level_info(level_number)
```

### Security Logging
```python
from banditgui.utils import security_logger

# Log session activity
security_logger.log_session_activity(
    session_id="123",
    action="create",
    details={"user": "john"}
)

# Log API request
security_logger.log_api_request(
    endpoint="/api/v1/levels",
    method="GET",
    status=200,
    details={"user_id": "123"}
)
```

### Rate Limiting
```python
from banditgui.utils import rate_limiter

# Check if action is allowed
if rate_limiter.is_allowed("user_123", "level_attempt"):
    # Proceed with action
    pass
```

## Testing

The utils directory includes comprehensive test coverage for all major components. Test files are located alongside their respective utility modules and can be run using standard Python testing frameworks.

## Contributing

When contributing to the utils directory:
1. Follow the existing code style and patterns
2. Add appropriate logging for new features
3. Include comprehensive test coverage
4. Document new utilities in this README
5. Maintain backward compatibility where possible

## License

[Insert license information here]
