"""
CSRF protection middleware for BanditGUI.
"""

import secrets
from functools import wraps
from typing import Callable

from flask import jsonify, request, session

from banditgui.config.logging_config import logger
from banditgui.utils.security_logger import security_logger


class CSRFProtection:
    """
    CSRF protection implementation.
    """
    def __init__(self, app=None):
        self.app = app
        if app is not None:
            self.init_app(app)

    def init_app(self, app):
        """
        Initialize CSRF protection with Flask app.
        """
        app.before_request(self._check_csrf)
        app.after_request(self._add_csrf_header)

    def _generate_csrf_token(self) -> str:
        """
        Generate a secure CSRF token.
        """
        return secrets.token_urlsafe(32)

    def _get_csrf_token(self) -> str:
        """
        Get or generate CSRF token for the session.
        """
        if '_csrf_token' not in session:
            session['_csrf_token'] = self._generate_csrf_token()
        return session['_csrf_token']

    def _check_csrf(self):
        """
        Check CSRF token on POST requests.
        """
        if request.method == 'POST':
            token = session.get('_csrf_token')
            if not token or token != request.headers.get('X-CSRF-Token'):
                logger.warning(f"CSRF token validation failed for {request.remote_addr}")
                return jsonify({
                    'status': 'error',
                    'message': 'Invalid CSRF token'
                }), 400

    def _add_csrf_header(self, response):
        """
        Add CSRF token to response headers.
        """
        if '_csrf_token' in session:
            response.headers['X-CSRF-Token'] = session['_csrf_token']
        return response

    def generate_token(self) -> str:
        """
        Generate and return a new CSRF token.
        """
        token = self._generate_csrf_token()
        session['_csrf_token'] = token
        return token

    def validate_token(self, token: str) -> bool:
        """
        Validate a CSRF token with security logging.
        """
        stored_token = session.get('_csrf_token')
        is_valid = stored_token == token
        
        security_logger.log_csrf_event(
            status="validated" if is_valid else "failed",
            details={
                "ip_address": request.remote_addr,
                "user_agent": request.headers.get('User-Agent', 'unknown'),
                "path": request.path,
                "method": request.method
            }
        )
        
        return is_valid

    def generate_token_decorator(self, func: Callable) -> Callable:
        """
        Flask route decorator to generate a new CSRF token before the view is called.
        """
        @wraps(func)
        def wrapper(*args, **kwargs):
            self.generate_token()
            return func(*args, **kwargs)
        return wrapper

    def validate_token_decorator(self, func: Callable) -> Callable:
        """
        Flask route decorator to validate CSRF token from request headers before the view is called.
        """
        @wraps(func)
        def wrapper(*args, **kwargs):
            token = request.headers.get('X-CSRF-Token')
            if not self.validate_token(token):
                logger.warning(f"CSRF token validation failed for {request.remote_addr}")
                return jsonify({'status': 'error', 'message': 'Invalid CSRF token'}), 400
            return func(*args, **kwargs)
        return wrapper

# Initialize CSRF protection
csrf = CSRFProtection()
