import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict


def setup_logger(name: str, log_dir: str = "logs", level: int = logging.INFO) -> logging.Logger:
    """
    Set up a structured logger with file and console handlers.
    
    Args:
        name: Name of the logger
        log_dir: Directory to store log files
        level: Logging level (default: INFO)
        
    Returns:
        Configured logger instance
    """
    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # Create log directory if it doesn't exist
    log_path = Path(log_dir)
    log_path.mkdir(parents=True, exist_ok=True)
    
    # Create log file name with timestamp
    timestamp = datetime.now().strftime("%Y-%m-%d")
    log_file = log_path / f"{name}_{timestamp}.log"
    
    # Create handlers
    file_handler = logging.FileHandler(log_file)
    console_handler = logging.StreamHandler()
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Add formatter to handlers
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

def log_exception(logger: logging.Logger, exc: Exception, details: Dict[str, Any] = None) -> None:
    """
    Log an exception with additional details.
    
    Args:
        logger: Logger instance
        exc: Exception to log
        details: Additional details to include in the log
    """
    error_type = type(exc).__name__
    message = str(exc)
    
    log_details = {
        "error_type": error_type,
        "message": message,
        "details": details or {}
    }
    
    logger.error(
        f"Exception occurred: {message}",
        exc_info=True,
        extra=log_details
    )

def log_performance(logger: logging.Logger, operation: str, duration: float, details: Dict[str, Any] = None) -> None:
    """
    Log performance metrics.
    
    Args:
        logger: Logger instance
        operation: Name of the operation
        duration: Duration in seconds
        details: Additional details to include in the log
    """
    log_details = {
        "operation": operation,
        "duration": duration,
        "details": details or {}
    }
    
    logger.info(
        f"Performance: {operation} completed in {duration:.2f}s",
        extra=log_details
    )
