"""
Rate limiting middleware for BanditGUI API endpoints.
"""

import time
from functools import wraps
from typing import Any, Callable, Dict

from flask import jsonify, request

from banditgui.config.logging_config import logger


class RateLimiter:
    """
    Rate limiting implementation using token bucket algorithm.
    """
    def __init__(self, max_tokens: int = 100, refill_rate: float = 1.0):
        """
        Initialize the rate limiter.
        
        Args:
            max_tokens: Maximum number of tokens in the bucket
            refill_rate: Rate at which tokens are refilled (tokens per second)
        """
        self.max_tokens = max_tokens
        self.refill_rate = refill_rate
        self.tokens = max_tokens
        self.last_refill = time.time()
        self.ip_limits: Dict[str, Dict] = {}
        self.endpoint_limits: Dict[str, Dict] = {}

    def _refill_tokens(self, ip: str, endpoint: str) -> None:
        """
        Refill tokens for a specific IP and endpoint.
        """
        now = time.time()
        time_since_last = now - self.ip_limits[ip][endpoint]['last_refill']
        tokens_to_add = time_since_last * self.refill_rate
        self.ip_limits[ip][endpoint]['tokens'] = min(
            self.ip_limits[ip][endpoint]['tokens'] + tokens_to_add,
            self.ip_limits[ip][endpoint]['max_tokens']
        )
        self.ip_limits[ip][endpoint]['last_refill'] = now

    def _initialize_limit(self, ip: str, endpoint: str, max_tokens: int, refill_rate: float) -> None:
        """
        Initialize rate limit for a specific IP and endpoint.
        """
        if ip not in self.ip_limits:
            self.ip_limits[ip] = {}
        if endpoint not in self.ip_limits[ip]:
            self.ip_limits[ip][endpoint] = {
                'max_tokens': max_tokens,
                'tokens': max_tokens,
                'refill_rate': refill_rate,
                'last_refill': time.time()
            }

    def check_limit(self, endpoint: str, ip: str, max_tokens: int = None, refill_rate: float = None) -> bool:
        """
        Check and update the rate limit for a specific IP and endpoint. Returns True if allowed, False if rate limited.
        """
        if max_tokens is None:
            max_tokens = self.max_tokens
        if refill_rate is None:
            refill_rate = self.refill_rate
        self._initialize_limit(ip, endpoint, max_tokens, refill_rate)
        self._refill_tokens(ip, endpoint)
        if self.ip_limits[ip][endpoint]['tokens'] < 1:
            return False
        return True

    def limit(self, max_tokens: int = 100, refill_rate: float = 1.0, per_endpoint: bool = True) -> Callable:
        """
        Decorator to apply rate limiting to a Flask route.
        
        Args:
            max_tokens: Maximum number of tokens for this endpoint
            refill_rate: Rate at which tokens are refilled (tokens per second)
            per_endpoint: If True, limit is per endpoint; if False, global limit
        """
        def decorator(f: Callable) -> Callable:
            @wraps(f)
            def wrapped_function(*args: Any, **kwargs: Any) -> Any:
                ip = request.remote_addr
                endpoint = request.endpoint

                if per_endpoint:
                    if not self.check_limit(endpoint, ip, max_tokens, refill_rate):
                        logger.warning(f"Rate limit exceeded for {ip} on {endpoint}")
                        return jsonify({
                            'status': 'error',
                            'message': 'Rate limit exceeded. Please wait a moment and try again.'
                        }), 429
                    self.ip_limits[ip][endpoint]['tokens'] -= 1
                else:
                    self._refill_tokens(ip, 'global')
                    if self.tokens < 1:
                        logger.warning(f"Global rate limit exceeded for {ip}")
                        return jsonify({
                            'status': 'error',
                            'message': 'Rate limit exceeded. Please wait a moment and try again.'
                        }), 429
                    self.tokens -= 1
                return f(*args, **kwargs)
            return wrapped_function
        return decorator

# Initialize rate limiter with default settings
rate_limiter = RateLimiter(max_tokens=100, refill_rate=1.0)
