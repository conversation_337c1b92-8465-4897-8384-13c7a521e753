"""
Security-specific logging utility for BanditGUI.

This module provides enhanced logging for security-related operations.
"""

import logging
from datetime import datetime
from typing import Any, Dict

from banditgui.utils.logging_utils import setup_logger


class SecurityLogger:
    """Utility class for security-specific logging."""
    
    def __init__(self, name: str = "security"):
        """
        Initialize the security logger.
        
        Args:
            name: Name of the logger (default: "security")
        """
        self.logger = setup_logger(name, log_dir="logs/security", level=logging.INFO)
    
    def log_session_activity(self, session_id: str, action: str, details: Dict[str, Any]) -> None:
        """
        Log session-related security events.
        
        Args:
            session_id: ID of the session
            action: Type of session action (e.g., "create", "update", "delete")
            details: Additional details about the action
        """
        log_data = {
            "event_type": "session_activity",
            "session_id": session_id,
            "action": action,
            "timestamp": datetime.now().isoformat(),
            **details
        }
        
        self.logger.info(
            f"Session {action} event: {session_id}",
            extra={"details": log_data}
        )
    
    def log_api_request(self, endpoint: str, method: str, status: int, details: Dict[str, Any]) -> None:
        """
        Log API request details.
        
        Args:
            endpoint: API endpoint URL
            method: HTTP method (e.g., "GET", "POST")
            status: HTTP status code
            details: Additional request details
        """
        log_data = {
            "event_type": "api_request",
            "endpoint": endpoint,
            "method": method,
            "status": status,
            "timestamp": datetime.now().isoformat(),
            **details
        }
        
        log_level = logging.INFO if status < 400 else logging.ERROR
        self.logger.log(
            log_level,
            f"API {method} {endpoint} - Status: {status}",
            extra={"details": log_data}
        )
    
    def log_authentication(self, provider: str, status: str, details: Dict[str, Any]) -> None:
        """
        Log authentication events.
        
        Args:
            provider: Authentication provider (e.g., "openai", "gemini")
            status: Authentication status (e.g., "success", "failure")
            details: Additional authentication details
        """
        log_data = {
            "event_type": "authentication",
            "provider": provider,
            "status": status,
            "timestamp": datetime.now().isoformat(),
            **details
        }
        
        log_level = logging.INFO if status == "success" else logging.ERROR
        self.logger.log(
            log_level,
            f"Authentication {status} for {provider}",
            extra={"details": log_data}
        )
    
    def log_security_event(self, event_type: str, details: Dict[str, Any]) -> None:
        """
        Log generic security events.
        
        Args:
            event_type: Type of security event
            details: Event details
        """
        log_data = {
            "event_type": event_type,
            "timestamp": datetime.now().isoformat(),
            **details
        }
        
        self.logger.info(
            f"Security event: {event_type}",
            extra={"details": log_data}
        )
    
    def log_rate_limit(self, endpoint: str, ip_address: str, remaining: int, limit: int) -> None:
        """
        Log rate limiting events.
        
        Args:
            endpoint: API endpoint being rate limited
            ip_address: IP address of the request
            remaining: Remaining requests in current window
            limit: Total requests allowed in window
        """
        log_data = {
            "event_type": "rate_limit",
            "endpoint": endpoint,
            "ip_address": ip_address,
            "remaining": remaining,
            "limit": limit,
            "timestamp": datetime.now().isoformat()
        }
        
        self.logger.info(
            f"Rate limit check for {endpoint} - Remaining: {remaining}/{limit}",
            extra={"details": log_data}
        )
    
    def log_csrf_event(self, status: str, details: Dict[str, Any]) -> None:
        """
        Log CSRF protection events.
        
        Args:
            status: Event status (e.g., "validated", "failed")
            details: Event details
        """
        log_data = {
            "event_type": "csrf",
            "status": status,
            "timestamp": datetime.now().isoformat(),
            **details
        }
        
        log_level = logging.INFO if status == "validated" else logging.WARNING
        self.logger.log(
            log_level,
            f"CSRF {status} event",
            extra={"details": log_data}
        )

# Initialize the security logger
security_logger = SecurityLogger()
