"""
Session management for BanditGUI.

This module handles secure session management, replacing the localStorage-based approach.
"""

import secrets
import time

from flask import session
from flask.sessions import SecureCookieSessionInterface


class SecureSessionManager:
    """
    Secure session management class.
    """
    def __init__(self, session_timeout: int = 3600):  # 1 hour default timeout
        """
        Initialize the session manager.
        
        Args:
            session_timeout: Number of seconds before session expires
        """
        self.session_timeout = session_timeout
        self.session_interface = SecureCookieSessionInterface()
        self.session_interface.salt = 'banditgui-session'
        self.session_interface.secret_key = secrets.token_hex(32)

    def create_session(self) -> str:
        """
        Create a new secure session.
        
        Returns:
            str: The session ID
        """
        session_id = secrets.token_hex(16)
        session['session_id'] = session_id
        session['created_at'] = time.time()
        session['last_activity'] = time.time()
        return session_id

    def validate_session(self) -> bool:
        """
        Validate the current session.
        
        Returns:
            bool: True if session is valid, False otherwise
        """
        if 'session_id' not in session:
            return False

        # Check session timeout
        if time.time() - session.get('last_activity', 0) > self.session_timeout:
            self.clear_session()
            return False

        # Update last activity
        session['last_activity'] = time.time()
        return True

    def clear_session(self):
        """
        Clear the current session.
        """
        session.clear()

# Initialize the session manager with a 1-hour timeout
session_manager = SecureSessionManager()
