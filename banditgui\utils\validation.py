from typing import Any, Dict

import jsonschema

from banditgui.exceptions import ProgressionError
from banditgui.utils.json_schema import json_schema_validator


class DataValidator:
    """Utility class for validating data structures in BanditGUI."""
    
    @staticmethod
    def validate_session_data(data: Dict[str, Any]) -> None:
        """
        Validate session data structure using JSON schema for a single session object.
        
        Args:
            data: Dictionary containing session data
            
        Raises:
            ProgressionError: If validation fails
        """
        try:
            schema = json_schema_validator._get_single_session_schema()
            jsonschema.validate(instance=data, schema=schema)
        except jsonschema.ValidationError as e:
            raise ProgressionError(
                f"Session data validation failed: {str(e)}",
                error_type='validation_failed',
                validation_error=str(e)
            )
    
    @staticmethod
    def validate_stats_data(data: Dict[str, Any]) -> None:
        """
        Validate stats data structure using JSON schema.
        
        Args:
            data: Dictionary containing stats data
            
        Raises:
            ProgressionError: If validation fails
        """
        try:
            schema = json_schema_validator._get_schema('stats.json')
            jsonschema.validate(instance=data, schema=schema)
        except jsonschema.ValidationError as e:
            raise ProgressionError(
                f"Stats data validation failed: {str(e)}",
                error_type='validation_failed',
                validation_error=str(e)
            )
    
    @staticmethod
    def validate_json_file(file_path: str) -> Dict:
        """
        Validate JSON file using JSON schema validation.
        
        Args:
            file_path: Path to the JSON file
            
        Returns:
            Dict: Validated JSON data
            
        Raises:
            ProgressionError: If validation fails
        """
        try:
            return json_schema_validator.validate_json_file(file_path)
        except ProgressionError as e:
            raise ProgressionError(
                f"JSON file validation failed: {str(e)}",
                error_type='validation_failed',
                file_path=file_path
            )
