# BanditGUI Error Handling Documentation

## Error Handling Architecture

BanditGUI implements a structured error handling system designed to:
- Provide consistent error responses
- Prevent information leakage
- Support debugging and monitoring
- Maintain system stability
- Enhance security through proper error management

## Error Types

### Custom Error Classes

1. **ProgressionError**
   - Base error for progression-related issues
   - Used for session management, validation failures, and state errors
   - Attributes:
     - `error_type`: String identifier for error category
     - `validation_error`: Detailed validation error message
     - `context`: Additional error context

2. **SecurityError**
   - Base error for security-related issues
   - Used for authentication, authorization, and validation failures
   - Attributes:
     - `security_event`: Type of security event
     - `operation`: Failed operation
     - `context`: Additional security context

3. **RateLimitError**
   - Error for rate limiting violations
   - Attributes:
     - `remaining_tokens`: Number of tokens remaining
     - `reset_time`: Time until rate limit resets
     - `endpoint`: Limited endpoint

4. **ValidationError**
   - Base error for data validation failures
   - Used with JSON schema validation
   - Attributes:
     - `validation_errors`: List of specific validation failures
     - `schema`: Name of failed schema
     - `context`: Validation context

## Error Response Structure

All error responses follow a consistent structure:

```json
{
    "status": "error",
    "error": {
        "type": "error_type",
        "message": "User-friendly error message",
        "details": {
            "code": "error_code",
            "context": "additional_context"
        }
    },
    "timestamp": "ISO_8601_timestamp"
}
```

## Error Handling Patterns

### Session Management Errors
```python
try:
    # Session operation
except ProgressionError as e:
    security_logger.log_session_activity(
        status="error",
        operation="session_update",
        details={"error": str(e)}
    )
    return jsonify({
        "status": "error",
        "error": {
            "type": "session_error",
            "message": "Session update failed",
            "details": {
                "code": e.error_type,
                "context": e.validation_error
            }
        }
    })
```

### Security Errors
```python
try:
    # Security-sensitive operation
except SecurityError as e:
    security_logger.log_authentication(
        provider="internal",
        status="failed",
        details={"error": str(e)}
    )
    return jsonify({
        "status": "error",
        "error": {
            "type": "security_error",
            "message": "Security check failed",
            "details": {
                "code": e.security_event,
                "context": e.context
            }
        }
    })
```

### Rate Limiting Errors
```python
try:
    rate_limiter.check_limit(ip_address, endpoint)
except RateLimitError as e:
    security_logger.log_rate_limit(
        ip_address=ip_address,
        endpoint=endpoint,
        status="exceeded",
        details={"remaining": e.remaining_tokens}
    )
    return jsonify({
        "status": "error",
        "error": {
            "type": "rate_limit",
            "message": "Rate limit exceeded",
            "details": {
                "reset_time": e.reset_time,
                "remaining": e.remaining_tokens
            }
        }
    })
```

### Data Validation Errors
```python
try:
    json_schema_validator.validate(data, schema_name)
except ValidationError as e:
    security_logger.log_validation(
        schema=schema_name,
        status="failed",
        details={"errors": str(e)}
    )
    return jsonify({
        "status": "error",
        "error": {
            "type": "validation_error",
            "message": "Data validation failed",
            "details": {
                "schema": schema_name,
                "errors": e.validation_errors
            }
        }
    })
```

## Error Recovery Strategies

1. **Session Recovery**
   - Automatic session refresh on timeout
   - Graceful degradation of features
   - User-friendly error messages

2. **Security Recovery**
   - Automatic key rotation on failure
   - Rate limit reset after timeout
   - Session cleanup on security events

3. **Validation Recovery**
   - Input sanitization before retry
   - Default values for missing fields
   - User guidance for correction

## Monitoring and Logging

### Error Logging
- All errors are logged with context
- Security events are logged separately
- Rate limiting events are tracked
- Validation failures are recorded

### Log Structure
```python
{
    "timestamp": "ISO_8601_timestamp",
    "level": "ERROR",
    "error_type": "error_category",
    "operation": "failed_operation",
    "context": {
        "user": "user_id",
        "ip": "ip_address",
        "endpoint": "endpoint_path",
        "details": "error_details"
    }
}
```

### Error Metrics
- Error rate per endpoint
- Security event frequency
- Rate limit violations
- Validation failures
- Session timeouts

## Best Practices

1. **Error Handling**
   - Always use specific error classes
   - Log errors with context
   - Provide user-friendly messages
   - Prevent information leakage

2. **Security**
   - Never expose sensitive data
   - Use generic error messages
   - Log security events separately
   - Implement proper cleanup

3. **Monitoring**
   - Track error rates
   - Monitor security events
   - Watch rate limiting
   - Track validation failures

4. **User Experience**
   - Provide clear error messages
   - Offer guidance for recovery
   - Maintain system stability
   - Handle edge cases gracefully
