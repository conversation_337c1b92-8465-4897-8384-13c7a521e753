# BanditGUI API Documentation

## Security Features

### Authentication & Session Management
- Secure cookie-based sessions with 30-minute timeout
- Automatic session refresh on activity
- CSRF token validation for all POST requests
- Secure session cookies (HTTPOnly, Secure, SameSite=Lax)
- Session state persistence with integrity checks
- Secure session cleanup on timeout
- Protection against session hijacking

### API Security
- Token bucket-based rate limiting
- ask-a-pro endpoint: 3 requests per minute
- All endpoints: 50 requests per hour
- Automatic rate limit refilling
- Detailed logging of rate limit events
- Protection against brute force attacks

### Input Validation
- JSON Schema validation for all data structures
- Input sanitization for all user inputs
- Protection against XSS and CSRF attacks
- Regular expression validation
- Command history validation
- Input length restrictions
- Data integrity checks

### API Key Security
- Environment variable-based API key storage
- Automatic key rotation every 24 hours
- Secure hashing and validation
- Generic error responses to prevent key enumeration
- Rate limiting per provider
- Detailed logging of key usage

### Security Monitoring
- Comprehensive security logging
- Authentication attempts logging
- Rate limit events tracking
- CSRF token validation logging
- API request monitoring
- Session activity tracking
- Error tracking with context

## Endpoints

### 1. Server Status
```http
GET /server-status
```

**Description:** Check the status of the SSH server

**Security:**
- CSRF token required
- Rate limited to 50 requests per hour

**Response:**
```json
{
    "status": "success",
    "serverStatus": {
        "status": "running",
        "message": "Server is operational"
    }
}
```

### 2. Connect to Server
```http
POST /connect
```

**Description:** Connect to the SSH server

**Security:**
- CSRF token required
- Rate limited to 50 requests per hour
- Validates session

**Request Body:**
```json
{
    "csrfToken": "your-csrf-token"
}
```

**Response:**
```json
{
    "status": "success",
    "message": "Connected to SSH server",
    "currentLevel": 0,
    "csrfToken": "new-csrf-token"
}
```

### 3. Execute Command
```http
POST /execute
```

**Description:** Execute a command on the SSH server

**Security:**
- CSRF token required
- Rate limited to 50 requests per hour
- Input sanitization
- Command validation

**Request Body:**
```json
{
    "command": "ls -la",
    "csrfToken": "your-csrf-token"
}
```

**Response:**
```json
{
    "status": "success",
    "output": "Command output...",
    "csrfToken": "new-csrf-token"
}
```

### 4. Ask-a-Pro
```http
POST /ask-a-pro
```

**Description:** Get AI-powered assistance for current level

**Security:**
- CSRF token required
- Rate limited to 3 requests per minute
- Input validation
- API key management

**Request Body:**
```json
{
    "llm": "openai/gpt-4",
    "level_name": 1,
    "level_description": "Description",
    "command_history": ["ls", "pwd"],
    "csrfToken": "your-csrf-token"
}
```

**Response:**
```json
{
    "status": "success",
    "message": "AI response...",
    "csrfToken": "new-csrf-token"
}
```

### 5. Level Information
```http
GET /level-info
```

**Description:** Get information about a specific level

**Security:**
- CSRF token required
- Rate limited to 50 requests per hour

**Request Parameters:**
- level: number

**Response:**
```json
{
    "status": "success",
    "level": {
        "name": "Level 1",
        "description": "Description",
        "commands": ["ls", "cat"]
    }
}
```

### 6. Progress Tracking

#### Start Progress Session
```http
POST /start-progress
```

**Description:** Start tracking progress for a new session

**Security:**
- CSRF token required
- Session validation

**Response:**
```json
{
    "status": "success",
    "sessionId": "session-id-123",
    "csrfToken": "new-csrf-token"
}
```

#### Update Progress
```http
POST /update-progress
```

**Description:** Update progress tracking

**Security:**
- CSRF token required
- Session validation
- Rate limited to 50 requests per hour

**Request Body:**
```json
{
    "level": 1,
    "commands": ["ls", "pwd"],
    "hints": 0,
    "csrfToken": "your-csrf-token"
}
```

**Response:**
```json
{
    "status": "success",
    "message": "Progress updated",
    "csrfToken": "new-csrf-token"
}
```

## Error Handling

All endpoints return standardized error responses:

```json
{
    "status": "error",
    "message": "Error message",
    "error_type": "error_code",
    "csrfToken": "new-csrf-token"
}
```

### Common Error Types
- `auth_failed`: Authentication error
- `rate_limit`: Rate limit exceeded
- `invalid_input`: Invalid input data
- `session_expired`: Session expired
- `api_key_missing`: Required API key not found
- `server_error`: Internal server error

## Security Headers

All responses include security headers:
- Strict-Transport-Security
- X-Content-Type-Options
- X-Frame-Options
- X-XSS-Protection
- Content-Security-Policy

## API Key Management

### Environment Variables Required:
- `FLASK_SECRET_KEY`: Session secret key
- `OPENAI_API_KEY`: OpenAI API key (if using OpenAI)
- Other provider API keys as needed

### Security Recommendations:
1. Never commit API keys to version control
2. Use environment variables for API keys
3. Regularly rotate API keys
4. Monitor API usage
5. Set up proper error handling for API key failures
