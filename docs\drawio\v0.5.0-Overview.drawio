<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/26.1.1 Chrome/132.0.6834.210 Electron/34.3.3 Safari/537.36" version="26.1.1">
  <diagram id="codeviz-diagram" name="System Diagram">
    <mxGraphModel dx="3004" dy="1554" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" background="#CCCCCC" math="0" shadow="0" adaptiveColors="auto">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="5901" value="" style="html=1;whiteSpace=wrap;container=1;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0;labelBackgroundColor=none;rounded=1;sketch=1;curveFitting=1;jiggle=2;align=center;fontSize=16;fontFamily=Lucida Console;fillColor=#cce5ff;strokeColor=#36393d;gradientColor=default;glass=1;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="40" y="1475.9978312295736" width="684.4734936887214" height="244.07891561478687" as="geometry" />
        </mxCell>
        <mxCell id="5909" value="AI APIs&lt;br&gt;LiteLLM, OpenAI, etc." style="rounded=0;whiteSpace=wrap;html=1;labelBackgroundColor=none;sketch=1;curveFitting=1;jiggle=2;align=center;fontSize=18;fontFamily=Lucida Console;fillColor=#1ba1e2;fontColor=#CCCC00;strokeColor=#006EAF;gradientColor=default;fontStyle=1" parent="5901" vertex="1">
          <mxGeometry x="238.1578312295738" y="120" width="168.1578312295738" height="84.0789156147869" as="geometry" />
        </mxCell>
        <mxCell id="5910" value="Remote SSH Servers&lt;br&gt;Paramiko Target" style="rounded=0;whiteSpace=wrap;html=1;labelBackgroundColor=none;sketch=1;curveFitting=1;jiggle=2;align=center;fontSize=18;fontFamily=Lucida Console;fillColor=#1ba1e2;fontColor=#CCCC00;strokeColor=#006EAF;gradientColor=default;fontStyle=1" parent="5901" vertex="1">
          <mxGeometry x="40" y="120" width="168.1578312295738" height="84.0789156147869" as="geometry" />
        </mxCell>
        <mxCell id="5911" value="Web Data Sources&lt;br&gt;Bandit Wargame Site, etc." style="rounded=0;whiteSpace=wrap;html=1;labelBackgroundColor=none;sketch=1;curveFitting=1;jiggle=2;align=center;fontSize=18;fontFamily=Lucida Console;fillColor=#1ba1e2;fontColor=#CCCC00;strokeColor=#006EAF;gradientColor=default;fontStyle=1" parent="5901" vertex="1">
          <mxGeometry x="436.3156624591476" y="120" width="168.1578312295738" height="84.0789156147869" as="geometry" />
        </mxCell>
        <mxCell id="5901_label" value="&lt;font style=&quot;font-size: 32px; color: rgb(102, 255, 255);&quot;&gt;&lt;b style=&quot;&quot;&gt;External Systems&lt;/b&gt;&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=#5C5C5C;spacing=5;rounded=0;sketch=1;curveFitting=1;jiggle=2;fontSize=16;fontFamily=Lucida Console;" parent="1" vertex="1">
          <mxGeometry x="53" y="1442.9978312295736" width="608.4734936887214" height="24" as="geometry" />
        </mxCell>
        <mxCell id="5902" value="" style="html=1;whiteSpace=wrap;container=1;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0;labelBackgroundColor=none;rounded=0;sketch=1;curveFitting=1;jiggle=2;align=center;fontSize=16;fontFamily=Lucida Console;gradientColor=default;fontColor=#CC0000;textShadow=1;labelBorderColor=default;" parent="1" vertex="1">
          <mxGeometry x="22" y="621" width="733" height="759" as="geometry" />
        </mxCell>
        <mxCell id="5903" value="Web Server &amp;amp; API&lt;br&gt;Flask" style="rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;sketch=1;curveFitting=1;jiggle=2;align=center;fontSize=18;fontFamily=Lucida Console;fillColor=#33FF33;strokeColor=#36393d;gradientColor=#003300;fontColor=#FFFF00;glass=1;shadow=1;" parent="5902" vertex="1">
          <mxGeometry x="213.99734936887214" y="224.84" width="168.1578312295738" height="84.0789156147869" as="geometry" />
        </mxCell>
        <mxCell id="5904" value="Frontend UI&lt;br&gt;JavaScript, xterm.js" style="rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;sketch=1;curveFitting=1;jiggle=2;align=center;fontSize=18;fontFamily=Lucida Console;fillColor=#33FF33;strokeColor=#36393d;gradientColor=#003300;fontColor=#FFFF00;glass=1;shadow=1;" parent="5902" vertex="1">
          <mxGeometry x="498.0036545738011" y="31.840000000000003" width="168.1578312295738" height="84.0789156147869" as="geometry" />
        </mxCell>
        <mxCell id="5905" value="SSH Connection Manager&lt;br&gt;Paramiko" style="rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;sketch=1;curveFitting=1;jiggle=2;align=center;fontSize=18;fontFamily=Lucida Console;fillColor=#33FF33;strokeColor=#36393d;gradientColor=#003300;fontColor=#FFFF00;glass=1;shadow=1;" parent="5902" vertex="1">
          <mxGeometry x="16" y="491.84" width="168.1578312295738" height="84.0789156147869" as="geometry" />
        </mxCell>
        <mxCell id="5906" value="Terminal Logic Manager&lt;br&gt;Python" style="rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;sketch=1;curveFitting=1;jiggle=2;align=center;fontSize=18;fontFamily=Lucida Console;fillColor=#33FF33;strokeColor=#36393d;gradientColor=#003300;fontColor=#FFFF00;glass=1;shadow=1;" parent="5902" vertex="1">
          <mxGeometry x="413.9956624591476" y="491.84000000000003" width="168.1578312295738" height="84.0789156147869" as="geometry" />
        </mxCell>
        <mxCell id="5907" value="Chat &amp;amp; AI Services&lt;br&gt;Python, LiteLLM" style="rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;sketch=1;curveFitting=1;jiggle=2;align=center;fontSize=18;fontFamily=Lucida Console;fillColor=#33FF33;strokeColor=#36393d;gradientColor=#003300;fontColor=#FFFF00;glass=1;shadow=1;" parent="5902" vertex="1">
          <mxGeometry x="218.9978312295738" y="491.84" width="168.1578312295738" height="84.0789156147869" as="geometry" />
        </mxCell>
        <mxCell id="5908" value="Data &amp;amp; Utility Services&lt;br&gt;Python" style="rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;sketch=1;curveFitting=1;jiggle=2;align=center;fontSize=18;fontFamily=Lucida Console;fillColor=#33FF33;strokeColor=#36393d;gradientColor=#003300;fontColor=#FFFF00;glass=1;shadow=1;" parent="5902" vertex="1">
          <mxGeometry x="413.99196766407647" y="660.84" width="168.1578312295738" height="84.0789156147869" as="geometry" />
        </mxCell>
        <mxCell id="5902_label" value="BanditGUI Web Application&lt;br&gt;Flask, xterm.js" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=#006600;spacing=5;rounded=0;sketch=1;curveFitting=1;jiggle=2;fontSize=32;fontFamily=Lucida Console;fontColor=#80FF00;" parent="1" vertex="1">
          <mxGeometry x="24.97" y="572.9978312295738" width="636.4997988936502" height="24" as="geometry" />
        </mxCell>
        <mxCell id="5912" value="User&lt;br&gt;External Actor" style="rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;sketch=1;curveFitting=1;jiggle=2;align=center;fontSize=16;fontFamily=Lucida Console;fillColor=#FFFF66;strokeColor=#b85450;glass=1;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="279.00419676640763" y="269" width="168.1578312295738" height="84.0789156147869" as="geometry" />
        </mxCell>
        <mxCell id="5913" value="Installation Script&lt;br&gt;Python" style="rounded=1;whiteSpace=wrap;html=1;labelBackgroundColor=none;sketch=1;curveFitting=1;jiggle=2;align=center;fontSize=16;fontFamily=Lucida Console;fillColor=#FFFF66;strokeColor=#b85450;glass=1;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="279.0047389590142" y="427.9989156147868" width="168.1578312295738" height="84.0789156147869" as="geometry" />
        </mxCell>
        <mxCell id="edge-3321" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=none;labelBorderColor=none;fontColor=default;sketch=1;curveFitting=1;jiggle=2;align=center;fontSize=16;fontFamily=Lucida Console;" parent="1" source="5912" target="5913" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-3321_label" value="Runs" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];labelBackgroundColor=none;rounded=0;sketch=1;curveFitting=1;jiggle=2;fontSize=16;fontFamily=Lucida Console;fontColor=#CC0000;textShadow=1;labelBorderColor=default;" parent="edge-3321" vertex="1" connectable="0">
          <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
            <mxPoint x="18" y="3" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-3322" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=none;labelBorderColor=none;fontColor=default;sketch=1;curveFitting=1;jiggle=2;align=center;fontSize=16;fontFamily=Lucida Console;" parent="1" source="5912" target="5904" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-3322_label" value="Interacts with" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];labelBackgroundColor=none;rounded=0;sketch=1;curveFitting=1;jiggle=2;fontSize=16;fontFamily=Lucida Console;fontColor=#CC0000;textShadow=1;labelBorderColor=default;" parent="edge-3322" vertex="1" connectable="0">
          <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
            <mxPoint x="-85" y="-61" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-3324" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=none;labelBorderColor=none;fontColor=default;sketch=1;curveFitting=1;jiggle=2;align=center;fontSize=16;fontFamily=Lucida Console;" parent="1" source="5904" target="5903" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="537" y="750" />
              <mxPoint x="382" y="750" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-3324_label" value="Makes API calls to" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];labelBackgroundColor=none;rounded=0;sketch=1;curveFitting=1;jiggle=2;fontSize=16;fontFamily=Lucida Console;fontColor=#CC0000;textShadow=1;labelBorderColor=default;" parent="edge-3324" vertex="1" connectable="0">
          <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
            <mxPoint x="-13" y="-32" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-3325" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=none;labelBorderColor=none;fontColor=default;sketch=1;curveFitting=1;jiggle=2;align=center;fontSize=16;fontFamily=Lucida Console;" parent="1" source="5903" target="5904" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="654" y="848" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-3325_label" value="Serves" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];labelBackgroundColor=none;rounded=0;sketch=1;curveFitting=1;jiggle=2;fontSize=16;fontFamily=Lucida Console;fontColor=#CC0000;textShadow=1;labelBorderColor=default;" parent="edge-3325" vertex="1" connectable="0">
          <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
            <mxPoint x="-104" y="-15" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-3326" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=none;labelBorderColor=none;fontColor=default;sketch=1;curveFitting=1;jiggle=2;align=center;fontSize=16;fontFamily=Lucida Console;" parent="1" source="5903" target="5905" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge-3326_label" value="Uses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];labelBackgroundColor=none;rounded=0;sketch=1;curveFitting=1;jiggle=2;fontSize=16;fontFamily=Lucida Console;fontColor=#CC0000;textShadow=1;labelBorderColor=default;" parent="edge-3326" vertex="1" connectable="0">
          <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
            <mxPoint x="69" y="-6" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-3327" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=none;labelBorderColor=none;fontColor=default;sketch=1;curveFitting=1;jiggle=2;align=center;fontSize=16;fontFamily=Lucida Console;" parent="1" source="5903" target="5906" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="351" y="1035" />
              <mxPoint x="520" y="1035" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-3327_label" value="Uses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];labelBackgroundColor=none;rounded=0;sketch=1;curveFitting=1;jiggle=2;fontSize=16;fontFamily=Lucida Console;fontColor=#CC0000;textShadow=1;labelBorderColor=default;" parent="edge-3327" vertex="1" connectable="0">
          <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
            <mxPoint x="-11" y="-74" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-3328" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=none;labelBorderColor=none;fontColor=default;sketch=1;curveFitting=1;jiggle=2;align=center;fontSize=16;fontFamily=Lucida Console;" parent="1" source="5903" target="5907" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="274" y="989" />
              <mxPoint x="287" y="989" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-3328_label" value="Uses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];labelBackgroundColor=none;rounded=0;sketch=1;curveFitting=1;jiggle=2;fontSize=16;fontFamily=Lucida Console;fontColor=#CC0000;textShadow=1;labelBorderColor=default;" parent="edge-3328" vertex="1" connectable="0">
          <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
            <mxPoint x="5" y="-48" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-3330" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=none;labelBorderColor=none;fontColor=default;sketch=1;curveFitting=1;jiggle=2;align=center;fontSize=16;fontFamily=Lucida Console;" parent="1" source="5903" target="5908" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="694" y="883" />
              <mxPoint x="694" y="1292" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-3330_label" value="Gets data from" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];labelBackgroundColor=none;rounded=0;sketch=1;curveFitting=1;jiggle=2;fontSize=16;fontFamily=Lucida Console;fontColor=#CC0000;textShadow=1;labelBorderColor=default;" parent="edge-3330" vertex="1" connectable="0">
          <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
            <mxPoint x="-224" y="-11" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-3329" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=none;labelBorderColor=none;fontColor=default;sketch=1;curveFitting=1;jiggle=2;align=center;fontSize=16;fontFamily=Lucida Console;" parent="1" source="5906" target="5908" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="588" y="1239" />
              <mxPoint x="520" y="1239" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-3329_label" value="Gets level info from" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];labelBackgroundColor=none;rounded=0;sketch=1;curveFitting=1;jiggle=2;fontSize=16;fontFamily=Lucida Console;fontColor=#CC0000;textShadow=1;labelBorderColor=default;" parent="edge-3329" vertex="1" connectable="0">
          <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
            <mxPoint x="-85" y="-32" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-3323" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=none;labelBorderColor=none;fontColor=default;sketch=1;curveFitting=1;jiggle=2;align=center;fontSize=16;fontFamily=Lucida Console;" parent="1" source="5913" target="5903" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="137" y="723" />
              <mxPoint x="271" y="723" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-3323_label" value="Sets up environment for" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];labelBackgroundColor=none;rounded=0;sketch=1;curveFitting=1;jiggle=2;fontSize=16;fontFamily=Lucida Console;fontColor=#CC0000;textShadow=1;labelBorderColor=default;" parent="edge-3323" vertex="1" connectable="0">
          <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
            <mxPoint x="10" y="-136" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-3332" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=none;labelBorderColor=none;fontColor=default;sketch=1;curveFitting=1;jiggle=2;align=center;fontSize=16;fontFamily=Lucida Console;" parent="1" source="5907" target="5909" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="356" y="1396" />
              <mxPoint x="362" y="1396" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-3332_label" value="Calls" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];labelBackgroundColor=none;rounded=0;sketch=1;curveFitting=1;jiggle=2;fontSize=16;fontFamily=Lucida Console;fontColor=#CC0000;textShadow=1;labelBorderColor=default;" parent="edge-3332" vertex="1" connectable="0">
          <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
            <mxPoint x="-54" y="-136" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-3331" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=none;labelBorderColor=none;fontColor=default;sketch=1;curveFitting=1;jiggle=2;align=center;fontSize=16;fontFamily=Lucida Console;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="5905" target="5910" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="199.9989156147871" y="1177.9967468443608" as="sourcePoint" />
            <mxPoint x="180.0789156147871" y="1670.1578312295737" as="targetPoint" />
            <Array as="points">
              <mxPoint x="132" y="1197" />
              <mxPoint x="132" y="1505" />
              <mxPoint x="146" y="1505" />
              <mxPoint x="146" y="1596" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-3331_label" value="Connects to" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];labelBackgroundColor=none;rounded=0;sketch=1;curveFitting=1;jiggle=2;fontSize=16;fontFamily=Lucida Console;fontColor=#CC0000;textShadow=1;labelBorderColor=default;" parent="edge-3331" vertex="1" connectable="0">
          <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
            <mxPoint x="51" y="-146" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-3333" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=none;labelBorderColor=none;fontColor=default;sketch=1;curveFitting=1;jiggle=2;align=center;fontSize=16;fontFamily=Lucida Console;" parent="1" source="5908" target="5911" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="550" y="1500" />
              <mxPoint x="580" y="1500" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edge-3333_label" value="Scrapes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];labelBackgroundColor=none;rounded=0;sketch=1;curveFitting=1;jiggle=2;fontSize=16;fontFamily=Lucida Console;fontColor=#CC0000;textShadow=1;labelBorderColor=default;" parent="edge-3333" vertex="1" connectable="0">
          <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
