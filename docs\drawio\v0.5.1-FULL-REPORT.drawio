<mxfile host="65bd71144e">
    <diagram id="codeviz-diagram" name="System Diagram">
        <mxGraphModel dx="18" dy="377" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="search-results-group-399789bba79b968205a08a6e71d29d61" value="Search Results" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="1" vertex="1">
                    <mxGeometry x="40" width="4510" height="1310" as="geometry"/>
                </mxCell>
                <mxCell id="section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture-wrapper" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" parent="search-results-group-399789bba79b968205a08a6e71d29d61" vertex="1">
                    <mxGeometry x="1050" y="450" width="1440" height="870" as="geometry"/>
                </mxCell>
                <mxCell id="banditGUI_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" value="BanditGUI Application&lt;br&gt;Web Application" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture-wrapper" vertex="1">
                    <mxGeometry x="1172" y="280" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="banditWargame_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" value="Bandit Wargame&lt;br&gt;External System" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture-wrapper" vertex="1">
                    <mxGeometry x="640" y="770" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="chatModule_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" value="Chat Module&lt;br&gt;Feature" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture-wrapper" vertex="1">
                    <mxGeometry x="490" y="600" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="configModule_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" value="Configuration&lt;br&gt;Settings" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture-wrapper" vertex="1">
                    <mxGeometry x="190" y="580" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="coreApp_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" value="Core Application&lt;br&gt;Python Flask" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture-wrapper" vertex="1">
                    <mxGeometry x="1192" y="440" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="dataModule_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" value="Data Module&lt;br&gt;Static Data" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture-wrapper" vertex="1">
                    <mxGeometry x="1240" y="640" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="modelsModule_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" value="Models Module&lt;br&gt;Data Structures" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture-wrapper" vertex="1">
                    <mxGeometry x="940" y="630" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="sshModule_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" value="SSH Module&lt;br&gt;Connectivity" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture-wrapper" vertex="1">
                    <mxGeometry x="640" y="610" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="staticAssets_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" value="Static Assets&lt;br&gt;Frontend Files" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture-wrapper" vertex="1">
                    <mxGeometry x="340" y="590" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="templatesModule_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" value="Templates&lt;br&gt;HTML Views" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture-wrapper" vertex="1">
                    <mxGeometry x="1090" y="640" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="terminalModule_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" value="Terminal Module&lt;br&gt;Feature" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture-wrapper" vertex="1">
                    <mxGeometry x="790" y="620" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="testsModule_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" value="Tests&lt;br&gt;Testing" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture-wrapper" vertex="1">
                    <mxGeometry x="1042" y="410" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="user_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" value="User&lt;br&gt;Human" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture-wrapper" vertex="1">
                    <mxGeometry x="1172" y="120" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="utilsModule_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" value="Utilities&lt;br&gt;Helper Functions" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture-wrapper" vertex="1">
                    <mxGeometry x="40" y="570" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture-wrapper_label" value="High-Level Architecture" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" parent="search-results-group-399789bba79b968205a08a6e71d29d61" vertex="1">
                    <mxGeometry x="1058" y="458" width="1364" height="24" as="geometry"/>
                </mxCell>
                <mxCell id="section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction-wrapper" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" parent="search-results-group-399789bba79b968205a08a6e71d29d61" vertex="1">
                    <mxGeometry x="2480" y="440" width="2040" height="700" as="geometry"/>
                </mxCell>
                <mxCell id="allDataJson_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" value="all_data.json&lt;br&gt;Static Data" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction-wrapper" vertex="1">
                    <mxGeometry x="490" y="270" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="apiKeyManagerPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" value="api_key_manager.py&lt;br&gt;API Key Management" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction-wrapper" vertex="1">
                    <mxGeometry x="1070" y="460" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="appPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" value="app.py&lt;br&gt;Flask App" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction-wrapper" vertex="1">
                    <mxGeometry x="985" y="120" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="chatManagerPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" value="chat_manager.py&lt;br&gt;Chat Logic" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction-wrapper" vertex="1">
                    <mxGeometry x="1090" y="300" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="commandsDataJson_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" value="commands_data.json&lt;br&gt;Game Data" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction-wrapper" vertex="1">
                    <mxGeometry x="790" y="250" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="geekQuotesJson_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" value="geek_quotes.json&lt;br&gt;Static Data" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction-wrapper" vertex="1">
                    <mxGeometry x="190" y="280" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="generalInfoJson_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" value="general_info.json&lt;br&gt;Static Data" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction-wrapper" vertex="1">
                    <mxGeometry x="640" y="260" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="getDataPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" value="get_data.py&lt;br&gt;Data Loader" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction-wrapper" vertex="1">
                    <mxGeometry x="230" y="120" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="levelsInfoJson_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" value="levels_info.json&lt;br&gt;Game Data" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction-wrapper" vertex="1">
                    <mxGeometry x="340" y="280" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="llmModelJson_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" value="llm_model.json&lt;br&gt;LLM Config" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction-wrapper" vertex="1">
                    <mxGeometry x="1220" y="450" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="loggingPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" value="logging.py&lt;br&gt;Logging Config" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction-wrapper" vertex="1">
                    <mxGeometry x="940" y="280" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="progressionManagerPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" value="progression_manager.py&lt;br&gt;Progression Logic" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction-wrapper" vertex="1">
                    <mxGeometry x="40" y="120" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="progressionPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" value="progression.py&lt;br&gt;Data Model" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction-wrapper" vertex="1">
                    <mxGeometry x="40" y="280" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="securityLoggerPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" value="security_logger.py&lt;br&gt;Security Logging" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction-wrapper" vertex="1">
                    <mxGeometry x="1390" y="600" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="sessionManagerPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" value="session_manager.py&lt;br&gt;Session Management" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction-wrapper" vertex="1">
                    <mxGeometry x="1540" y="270" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="settingsPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" value="settings.py&lt;br&gt;Configuration" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction-wrapper" vertex="1">
                    <mxGeometry x="1840" y="250" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="sshManagerPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" value="ssh_manager.py&lt;br&gt;SSH Connection" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction-wrapper" vertex="1">
                    <mxGeometry x="1390" y="440" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="staticDir_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" value="static/&lt;br&gt;Static Files" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction-wrapper" vertex="1">
                    <mxGeometry x="1690" y="260" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="templatesDir_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" value="templates/&lt;br&gt;HTML Templates" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction-wrapper" vertex="1">
                    <mxGeometry x="1240" y="290" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="terminalManagerPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" value="terminal_manager.py&lt;br&gt;Terminal Logic" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction-wrapper" vertex="1">
                    <mxGeometry x="1390" y="280" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction-wrapper_label" value="Mid-Level Component Interaction" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" parent="search-results-group-399789bba79b968205a08a6e71d29d61" vertex="1">
                    <mxGeometry x="2488" y="448" width="1964" height="24" as="geometry"/>
                </mxCell>
                <mxCell id="section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details-wrapper" value="" style="html=1;whiteSpace=wrap;container=1;fillColor=#dae8fc;strokeColor=#6c8ebf;dashed=1;fillOpacity=20;strokeWidth=2;containerType=none;recursiveResize=0;movable=1;resizable=1;autosize=0;dropTarget=0" parent="search-results-group-399789bba79b968205a08a6e71d29d61" vertex="1">
                    <mxGeometry x="50" y="470" width="1010" height="730" as="geometry"/>
                </mxCell>
                <mxCell id="appPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" value="app.py&lt;br&gt;Flask App Entry" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details-wrapper" vertex="1">
                    <mxGeometry x="810" y="120" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="banditguiTestsDir_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" value="banditgui/tests/&lt;br&gt;App Tests" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details-wrapper" vertex="1">
                    <mxGeometry x="60" y="470" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="commandsJson_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" value="commands_data.json&lt;br&gt;Game Commands" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details-wrapper" vertex="1">
                    <mxGeometry x="210" y="440" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="cssFiles_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" value="CSS Files&lt;br&gt;Styling" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details-wrapper" vertex="1">
                    <mxGeometry x="510" y="250" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="dataDir_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" value="data/&lt;br&gt;JSON Data Files" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details-wrapper" vertex="1">
                    <mxGeometry x="210" y="280" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="flaskApp_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" value="Flask Application&lt;br&gt;Framework Instance" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details-wrapper" vertex="1">
                    <mxGeometry x="810" y="280" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="getDataPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" value="get_data.py&lt;br&gt;Data Loader Utility" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details-wrapper" vertex="1">
                    <mxGeometry x="210" y="120" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="indexHtml_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" value="index.html&lt;br&gt;Main SPA Entry" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details-wrapper" vertex="1">
                    <mxGeometry x="660" y="280" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="jsFiles_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" value="JavaScript Files&lt;br&gt;Client-side Logic" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details-wrapper" vertex="1">
                    <mxGeometry x="360" y="280" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="pytestFramework_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" value="Pytest&lt;br&gt;Testing Framework" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details-wrapper" vertex="1">
                    <mxGeometry x="60" y="150" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="staticDir_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" value="static/&lt;br&gt;Frontend Assets" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details-wrapper" vertex="1">
                    <mxGeometry x="380" y="120" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="templatesDir_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" value="templates/&lt;br&gt;HTML Templates" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details-wrapper" vertex="1">
                    <mxGeometry x="660" y="120" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="testAppPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" value="test_app.py&lt;br&gt;Application Tests" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details-wrapper" vertex="1">
                    <mxGeometry x="40" y="630" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="testProgressionManagerPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" value="test_progression_manager.py&lt;br&gt;Utility Tests" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details-wrapper" vertex="1">
                    <mxGeometry x="190" y="600" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="testsDir_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" value="tests/&lt;br&gt;Test Suites" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details-wrapper" vertex="1">
                    <mxGeometry x="60" y="310" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="webpackConfig_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" value="webpack.config.js&lt;br&gt;JS Bundler Config" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details-wrapper" vertex="1">
                    <mxGeometry x="360" y="440" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details-wrapper_label" value="Low-Level Implementation Details" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;labelBackgroundColor=white;spacing=5" parent="search-results-group-399789bba79b968205a08a6e71d29d61" vertex="1">
                    <mxGeometry x="58" y="478" width="934" height="24" as="geometry"/>
                </mxCell>
                <mxCell id="search-result-399789bba79b968205a08a6e71d29d61" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="search-results-group-399789bba79b968205a08a6e71d29d61" vertex="1">
                    <mxGeometry x="495" y="80" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="report-section-399789bba79b968205a08a6e71d29d61-0" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="search-results-group-399789bba79b968205a08a6e71d29d61" vertex="1">
                    <mxGeometry x="495" y="190" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="report-section-399789bba79b968205a08a6e71d29d61-1" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="search-results-group-399789bba79b968205a08a6e71d29d61" vertex="1">
                    <mxGeometry x="1680" y="280" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="report-section-399789bba79b968205a08a6e71d29d61-3" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="search-results-group-399789bba79b968205a08a6e71d29d61" vertex="1">
                    <mxGeometry x="3410" y="270" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="report-section-399789bba79b968205a08a6e71d29d61-12" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="search-results-group-399789bba79b968205a08a6e71d29d61" vertex="1">
                    <mxGeometry x="465" y="300" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="terminal-399789bba79b968205a08a6e71d29d61" value="Terminal" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5" parent="1" vertex="1">
                    <mxGeometry x="2207" y="1522" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="edge-edge-search-to-terminal-399789bba79b968205a08a6e71d29d61" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="search-results-group-399789bba79b968205a08a6e71d29d61" target="terminal-399789bba79b968205a08a6e71d29d61" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-edge-search-to-terminal-399789bba79b968205a08a6e71d29d61_label" value="Provides context to" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-edge-search-to-terminal-399789bba79b968205a08a6e71d29d61" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_user_banditGUI_0_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="user_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" target="banditGUI_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_user_banditGUI_0_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture_label" value="interacts with" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_user_banditGUI_0_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_banditGUI_coreApp_1_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="banditGUI_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" target="coreApp_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_banditGUI_coreApp_1_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture_label" value="orchestrates" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_banditGUI_coreApp_1_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_coreApp_configModule_2_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="coreApp_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" target="configModule_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_coreApp_configModule_2_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture_label" value="uses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_coreApp_configModule_2_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_coreApp_chatModule_3_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="coreApp_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" target="chatModule_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_coreApp_chatModule_3_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture_label" value="integrates" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_coreApp_chatModule_3_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_coreApp_terminalModule_4_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="coreApp_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" target="terminalModule_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_coreApp_terminalModule_4_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture_label" value="integrates" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_coreApp_terminalModule_4_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_coreApp_sshModule_5_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="coreApp_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" target="sshModule_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_coreApp_sshModule_5_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture_label" value="integrates" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_coreApp_sshModule_5_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_coreApp_dataModule_6_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="coreApp_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" target="dataModule_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_coreApp_dataModule_6_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture_label" value="uses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_coreApp_dataModule_6_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_coreApp_modelsModule_7_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="coreApp_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" target="modelsModule_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_coreApp_modelsModule_7_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture_label" value="uses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_coreApp_modelsModule_7_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_coreApp_utilsModule_8_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="coreApp_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" target="utilsModule_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_coreApp_utilsModule_8_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture_label" value="uses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_coreApp_utilsModule_8_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_coreApp_staticAssets_9_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="coreApp_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" target="staticAssets_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_coreApp_staticAssets_9_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture_label" value="serves" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_coreApp_staticAssets_9_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_coreApp_templatesModule_10_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="coreApp_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" target="templatesModule_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_coreApp_templatesModule_10_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture_label" value="renders" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_coreApp_templatesModule_10_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_sshModule_banditWargame_11_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="sshModule_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" target="banditWargame_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_sshModule_banditWargame_11_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture_label" value="connects to" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_sshModule_banditWargame_11_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_banditGUI_testsModule_12_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="banditGUI_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" target="testsModule_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_banditGUI_testsModule_12_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture_label" value="tested by" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_banditGUI_testsModule_12_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-report-section-399789bba79b968205a08a6e71d29d61-1-to-section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture-wrapper" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="report-section-399789bba79b968205a08a6e71d29d61-1" target="section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture-wrapper" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-report-section-399789bba79b968205a08a6e71d29d61-1-to-section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture-wrapper_label" value="Diagram" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-report-section-399789bba79b968205a08a6e71d29d61-1-to-section-diagram-399789bba79b968205a08a6e71d29d61-High-Level-Architecture-wrapper" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_appPy_settingsPy_0_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="appPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" target="settingsPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_appPy_settingsPy_0_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction_label" value="reads" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_appPy_settingsPy_0_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_appPy_loggingPy_1_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="appPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" target="loggingPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_appPy_loggingPy_1_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction_label" value="configures" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_appPy_loggingPy_1_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_appPy_sessionManagerPy_2_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="appPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" target="sessionManagerPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_appPy_sessionManagerPy_2_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction_label" value="uses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_appPy_sessionManagerPy_2_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_appPy_terminalManagerPy_3_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="appPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" target="terminalManagerPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_appPy_terminalManagerPy_3_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction_label" value="imports" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_appPy_terminalManagerPy_3_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_appPy_chatManagerPy_4_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="appPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" target="chatManagerPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_appPy_chatManagerPy_4_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction_label" value="imports" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_appPy_chatManagerPy_4_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_appPy_templatesDir_5_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="appPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" target="templatesDir_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_appPy_templatesDir_5_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction_label" value="renders" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_appPy_templatesDir_5_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_appPy_staticDir_6_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="appPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" target="staticDir_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_appPy_staticDir_6_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction_label" value="serves" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_appPy_staticDir_6_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_chatManagerPy_llmModelJson_7_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="chatManagerPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" target="llmModelJson_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_chatManagerPy_llmModelJson_7_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction_label" value="uses config" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_chatManagerPy_llmModelJson_7_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_chatManagerPy_apiKeyManagerPy_8_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="chatManagerPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" target="apiKeyManagerPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_chatManagerPy_apiKeyManagerPy_8_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction_label" value="uses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_chatManagerPy_apiKeyManagerPy_8_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_terminalManagerPy_sshManagerPy_9_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="terminalManagerPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" target="sshManagerPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_terminalManagerPy_sshManagerPy_9_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction_label" value="interacts with" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_terminalManagerPy_sshManagerPy_9_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_sshManagerPy_securityLoggerPy_10_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="sshManagerPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" target="securityLoggerPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_sshManagerPy_securityLoggerPy_10_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction_label" value="logs to" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_sshManagerPy_securityLoggerPy_10_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_getDataPy_commandsDataJson_11_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="getDataPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" target="commandsDataJson_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_getDataPy_commandsDataJson_11_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction_label" value="loads" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_getDataPy_commandsDataJson_11_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_getDataPy_levelsInfoJson_12_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="getDataPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" target="levelsInfoJson_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_getDataPy_levelsInfoJson_12_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction_label" value="loads" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_getDataPy_levelsInfoJson_12_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_getDataPy_geekQuotesJson_13_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="getDataPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" target="geekQuotesJson_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_getDataPy_geekQuotesJson_13_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction_label" value="loads" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_getDataPy_geekQuotesJson_13_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_getDataPy_allDataJson_14_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="getDataPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" target="allDataJson_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_getDataPy_allDataJson_14_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction_label" value="loads" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_getDataPy_allDataJson_14_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_getDataPy_generalInfoJson_15_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="getDataPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" target="generalInfoJson_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_getDataPy_generalInfoJson_15_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction_label" value="loads" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_getDataPy_generalInfoJson_15_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_progressionManagerPy_progressionPy_16_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="progressionManagerPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" target="progressionPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_progressionManagerPy_progressionPy_16_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction_label" value="uses model" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_progressionManagerPy_progressionPy_16_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-report-section-399789bba79b968205a08a6e71d29d61-3-to-section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction-wrapper" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="report-section-399789bba79b968205a08a6e71d29d61-3" target="section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction-wrapper" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-report-section-399789bba79b968205a08a6e71d29d61-3-to-section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction-wrapper_label" value="Diagram" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-report-section-399789bba79b968205a08a6e71d29d61-3-to-section-diagram-399789bba79b968205a08a6e71d29d61-Mid-Level-Component-Interaction-wrapper" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_appPy_flaskApp_0_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="appPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" target="flaskApp_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_appPy_flaskApp_0_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details_label" value="initializes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_appPy_flaskApp_0_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_appPy_flaskApp_1_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="appPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" target="flaskApp_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_appPy_flaskApp_1_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details_label" value="runs" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_appPy_flaskApp_1_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_dataDir_commandsJson_2_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="dataDir_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" target="commandsJson_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_dataDir_commandsJson_2_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details_label" value="contains" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_dataDir_commandsJson_2_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_getDataPy_dataDir_3_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="getDataPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" target="dataDir_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_getDataPy_dataDir_3_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details_label" value="loads and parses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_getDataPy_dataDir_3_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_templatesDir_indexHtml_4_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="templatesDir_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" target="indexHtml_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_templatesDir_indexHtml_4_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details_label" value="includes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_templatesDir_indexHtml_4_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_staticDir_cssFiles_5_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="staticDir_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" target="cssFiles_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_staticDir_cssFiles_5_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details_label" value="contains" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_staticDir_cssFiles_5_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_staticDir_jsFiles_6_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="staticDir_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" target="jsFiles_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_staticDir_jsFiles_6_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details_label" value="contains" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_staticDir_jsFiles_6_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_jsFiles_webpackConfig_7_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="jsFiles_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" target="webpackConfig_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_jsFiles_webpackConfig_7_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details_label" value="bundled by" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_jsFiles_webpackConfig_7_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_pytestFramework_testsDir_8_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="pytestFramework_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" target="testsDir_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_pytestFramework_testsDir_8_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details_label" value="executes tests in" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_pytestFramework_testsDir_8_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_testsDir_banditguiTestsDir_9_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="testsDir_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" target="banditguiTestsDir_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_testsDir_banditguiTestsDir_9_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details_label" value="includes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_testsDir_banditguiTestsDir_9_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_banditguiTestsDir_testAppPy_10_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="banditguiTestsDir_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" target="testAppPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_banditguiTestsDir_testAppPy_10_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details_label" value="contains" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_banditguiTestsDir_testAppPy_10_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-L_banditguiTestsDir_testProgressionManagerPy_11_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="banditguiTestsDir_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" target="testProgressionManagerPy_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-L_banditguiTestsDir_testProgressionManagerPy_11_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details_label" value="contains" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-L_banditguiTestsDir_testProgressionManagerPy_11_399789bba79b968205a08a6e71d29d61_section_section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-report-section-399789bba79b968205a08a6e71d29d61-12-to-section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details-wrapper" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="report-section-399789bba79b968205a08a6e71d29d61-12" target="section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details-wrapper" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-report-section-399789bba79b968205a08a6e71d29d61-12-to-section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details-wrapper_label" value="Diagram" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="edge-report-section-399789bba79b968205a08a6e71d29d61-12-to-section-diagram-399789bba79b968205a08a6e71d29d61-Low-Level-Implementation-Details-wrapper" vertex="1" connectable="0">
                    <mxGeometry x="-0.2" y="10" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-search-result-399789bba79b968205a08a6e71d29d61-to-report-section-399789bba79b968205a08a6e71d29d61-0" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="search-result-399789bba79b968205a08a6e71d29d61" target="report-section-399789bba79b968205a08a6e71d29d61-0" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-report-section-399789bba79b968205a08a6e71d29d61-0-to-report-section-399789bba79b968205a08a6e71d29d61-1" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="report-section-399789bba79b968205a08a6e71d29d61-0" target="report-section-399789bba79b968205a08a6e71d29d61-1" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-report-section-399789bba79b968205a08a6e71d29d61-0-to-report-section-399789bba79b968205a08a6e71d29d61-3" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="report-section-399789bba79b968205a08a6e71d29d61-0" target="report-section-399789bba79b968205a08a6e71d29d61-3" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge-report-section-399789bba79b968205a08a6e71d29d61-0-to-report-section-399789bba79b968205a08a6e71d29d61-12" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#808080;strokeWidth=2;jumpStyle=arc;jumpSize=10;spacing=15;labelBackgroundColor=white;labelBorderColor=none" parent="1" source="report-section-399789bba79b968205a08a6e71d29d61-0" target="report-section-399789bba79b968205a08a6e71d29d61-12" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>