# v0.2-FINAL - BanditGUI

## Summary
  
**Nermaid Code**  
  
> Note that the `MCP Filesystem Server` is only use for development and is not part of the final product.
  
```mermaid
graph TD

    43955["User<br>External Actor"]
    43960["Operating System Filesystem<br>External System"]
    subgraph 43952["Bandit System"]
        43953["MCP Filesystem Server"]
        43954["BanditGUI Web Application"]
    end
    %% Edges at this level (grouped by source)
    43955["User<br>External Actor"] -->|Uses| 43954["BanditGUI Web Application"]
    43953["MCP Filesystem Server"] -->|Accesses| 43960["Operating System Filesystem<br>External System"]
```
  
---
  
## Full View  
  
> Note that the `MCP Filesystem Server` is only use for development and is not part of the final product.
  
```mermaid
graph TD

    43955["User<br>External Actor"]
    43960["Operating System Filesystem<br>External System"]
    subgraph 43952["Bandit System"]
        43953["MCP Filesystem Server"]
        subgraph 43954["BanditGUI Web Application"]
            43956["GUI Application Logic<br>JavaScript"]
            43957["Terminal Interface<br>JavaScript"]
            43958["Xterm.js Library &amp; Addons<br>JavaScript"]
        end
    end
    %% Edges at this level (grouped by source)
    43953["MCP Filesystem Server"] -->|Accesses| 43960["Operating System Filesystem<br>External System"]
    43955["User<br>External Actor"] -->|Uses| 43956["GUI Application Logic<br>JavaScript"]
```
