# v0.2-FINAL - BanditGUI

## Overview

```mermaid
graph TD
    subgraph Frontend
        banditApp[BanditApp]
        terminal[Terminal UI]
        chat[Chat Interface]
        xterm[xterm.js]
        fitAddon[FitAddon]
    end

    subgraph Backend
        websocket[WebSocket Server]
        fileSystem[File System]
    end

    subgraph Core
        eventHandlers[Event Handlers]
        terminalState[Terminal State]
        commandProcessor[Command Processor]
        sessionManager[Session Manager]
    end


    banditApp --> terminal
    banditApp --> chat
    terminal --> xterm
    xterm --> fitAddon


    banditApp --> eventHandlers
    eventHandlers --> terminalState
    eventHandlers --> commandProcessor
    commandProcessor --> sessionManager


    sessionManager --> websocket
    sessionManager --> fileSystem


    websocket -->|Terminal Data| xterm
    xterm -->|User Input| websocket
    chat -->|Commands| commandProcessor
    commandProcessor -->|Responses| chat
    terminalState -->|Updates| terminal


    sessionManager -->|Session Info| terminalState
    commandProcessor -->|Command State| terminalState
 ```
