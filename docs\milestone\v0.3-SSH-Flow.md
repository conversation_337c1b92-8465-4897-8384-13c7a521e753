# v0.3 - BanditGUI SSH Flow

## Overview

The BanditGUI application uses <PERSON><PERSON><PERSON> to establish SSH connections to the OverTheWire Bandit server. This document outlines the simplified SSH flow as implemented in the application.

## SSH Connection Flow

```mermaid
graph TD
    User["User/Client"]
    BanditGUI["BanditGUI Application"]
    SSHManager["SSH Manager"]
    SSHServer["Bandit SSH Server"]
    Logger["Logging Service"]
    TerminalManager["Terminal Manager"]

    User -->|"1. Initiates Connection"| BanditGUI
    BanditGUI -->|"2. Connect Request"| SSHManager
    SSHManager -->|"3. Create SSH Client"| SSHManager
    SSHManager -->|"4. Set Host Key Policy"| SSHManager
    SSHManager -->|"5. Connect with Credentials"| SSHServer

    SSHServer -->|"6a. Connection Success"| SSHManager
    SSHServer -->|"6b. Connection Failure"| SSHManager

    SSHManager -->|"7. Log Connection Status"| Logger
    SSHManager -->|"8. Return Connection Result"| BanditGUI
    BanditGUI -->|"9. Update Terminal Status"| TerminalManager

    TerminalManager -->|"10. Execute Commands"| SSHManager
    SSHManager -->|"11. Send Commands"| SSHServer
    SSHServer -->|"12. Return Command Output"| SSHManager
    SSHManager -->|"13. Display Output"| BanditGUI
```

## Implementation Details

- **Authentication**: BanditGUI uses password authentication with the Bandit server
- **Command Execution**: Commands entered in the terminal are sent to the SSH server via the SSHManager
- **Error Handling**: Connection and command execution errors are logged and displayed to the user
- **Auto-Connection**: Some commands (like SSH commands) will attempt to auto-connect if not already connected

## Key Components

- **SSHManager**: Handles SSH connections and command execution
- **TerminalManager**: Manages terminal interactions and forwards commands to SSHManager
- **Configuration**: SSH connection details are stored in environment variables
