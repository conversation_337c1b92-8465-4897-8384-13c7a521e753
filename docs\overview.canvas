{"nodes": [{"type": "text", "id": "a3b5797d-d03b-4e0c-b4d7-ddb89328c5e0", "x": -349, "y": 174, "width": 106, "height": 108, "color": "6", "text": "## <center>User</center>  \n <center>Player</center> "}, {"type": "nested-canvas", "id": "fecfa567-e2e1-4896-a48c-ed86a5e9af71", "x": -38.105103312989854, "y": 178.73960886205782, "width": 420, "height": 180, "canvas": {"nodes": [{"type": "text", "id": "f6a874bb-e2a2-4f8f-8867-d79616b621b2", "x": 268, "y": 308.57142857142856, "width": 173, "height": 108, "color": "6", "text": "\n---\nfiles: [\"banditgui/app.py\"]\n---\n\n \n ## <center>Web Server</center>  \n  <center>Python Flask</center> "}, {"type": "text", "id": "15f7f6e6-87dd-4340-94f0-571706bf0899", "x": 906, "y": 586, "width": 235, "height": 108, "color": "6", "text": "\n---\nfiles: [\"banditgui/terminal/terminal_manager.py\"]\n---\n\n \n ## <center>Terminal Manager</center>  \n  <center>Python</center> "}, {"type": "text", "id": "cd2324f4-4c6e-4759-9301-4cbbea68434c", "x": 891, "y": 270, "width": 197, "height": 108, "color": "6", "text": "\n---\nfiles: [\"banditgui/chat/chat_manager.py\"]\n---\n\n \n ## <center>Chat Manager</center>  \n  <center>Python</center> "}, {"type": "text", "id": "9e400c9d-6c74-4fd7-a4d0-69b79e148c33", "x": 1306, "y": 586, "width": 182, "height": 108, "color": "6", "text": "\n---\nfiles: [\"banditgui/ssh/ssh_manager.py\"]\n---\n\n \n ## <center>SSH Handler</center>  \n  <center>Python Paramiko</center> "}, {"type": "text", "id": "85b26ae0-76a3-48d9-b23a-91b642a62425", "x": 891, "y": 902, "width": 265, "height": 108, "color": "6", "text": "\n---\nfiles: [\"banditgui/utils/progression_manager.py\"]\n---\n\n \n ## <center>Progression Manager</center>  \n  <center>Python</center> "}, {"type": "text", "id": "2719c9e3-c264-4d8d-853d-cc421f268d97", "x": 891, "y": 744, "width": 223, "height": 108, "color": "6", "text": "\n---\nfiles: [\"banditgui/utils/session_manager.py\"]\n---\n\n \n ## <center>Session Manager</center>  \n  <center>Python</center> "}, {"type": "text", "id": "a6cfaa8b-b79a-4fb2-8824-5f9f042d4b97", "x": 891, "y": 428, "width": 212, "height": 108, "color": "6", "text": "\n---\nfiles: [\"banditgui/utils/security_logger.py\"]\n---\n\n \n ## <center>Security Logger</center>  \n  <center>Python</center> "}, {"type": "text", "id": "0a0ddd08-2d73-4814-96c6-016a584bf3a1", "x": 12, "y": 12, "width": 226, "height": 108, "color": "6", "text": "\n---\nfiles: [\"banditgui/utils/api_key_manager.py\"]\n---\n\n \n ## <center>API Key Manager</center>  \n  <center>Python</center> "}, {"type": "text", "id": "75489726-902b-4c02-952f-fc77abb61a72", "x": 1306, "y": 920, "width": 242, "height": 108, "color": "6", "text": "\n---\nfiles: [\"banditgui/utils/level_info.py\",\"banditgui/data/levels_info.json\",\"banditgui/data/general_info.json\",\"banditgui/data/geek_quotes.json\",\"banditgui/data/commands_data.json\",\"banditgui/data/all_data.json\"]\n---\n\n \n ## <center>Level Data Handler</center>  \n  <center>Python</center> "}, {"type": "text", "id": "961f7686-96e9-4cd3-9511-16c42dfcb6a8", "x": 12, "y": 308.57142857142856, "width": 106, "height": 108, "color": "1", "text": "## <center>User</center>  \n <center>Player</center> "}], "edges": [{"id": "3c9d31b1-5712-400b-857e-94dc2c6f19ad", "fromNode": "f6a874bb-e2a2-4f8f-8867-d79616b621b2", "fromSide": "right", "toNode": "15f7f6e6-87dd-4340-94f0-571706bf0899", "toSide": "left", "toEnd": "arrow", "label": "Manages terminal"}, {"id": "a673d0eb-d911-43fb-9a5b-725f84534001", "fromNode": "f6a874bb-e2a2-4f8f-8867-d79616b621b2", "fromSide": "right", "toNode": "cd2324f4-4c6e-4759-9301-4cbbea68434c", "toSide": "left", "toEnd": "arrow", "label": "Handles chat"}, {"id": "5410e44e-9d67-4dad-91ea-34c5f5110f8e", "fromNode": "f6a874bb-e2a2-4f8f-8867-d79616b621b2", "fromSide": "right", "toNode": "2719c9e3-c264-4d8d-853d-cc421f268d97", "toSide": "left", "toEnd": "arrow", "label": "Manages sessions"}, {"id": "e4b500a8-0b87-42d8-ae1e-950ec8f4c825", "fromNode": "f6a874bb-e2a2-4f8f-8867-d79616b621b2", "fromSide": "right", "toNode": "85b26ae0-76a3-48d9-b23a-91b642a62425", "toSide": "left", "toEnd": "arrow", "label": "Updates progress"}, {"id": "f85337a0-baf5-4034-9286-93036deca692", "fromNode": "f6a874bb-e2a2-4f8f-8867-d79616b621b2", "fromSide": "right", "toNode": "a6cfaa8b-b79a-4fb2-8824-5f9f042d4b97", "toSide": "left", "toEnd": "arrow", "label": "Logs events"}, {"id": "99d9f41e-9660-46e3-92af-06b45388f5a7", "fromNode": "f6a874bb-e2a2-4f8f-8867-d79616b621b2", "fromSide": "right", "toNode": "75489726-902b-4c02-952f-fc77abb61a72", "toSide": "left", "toEnd": "arrow", "label": "Retrieves level info"}, {"id": "7ffc3951-ddaf-4ac5-b756-4fb3c589fd2f", "fromNode": "15f7f6e6-87dd-4340-94f0-571706bf0899", "fromSide": "right", "toNode": "9e400c9d-6c74-4fd7-a4d0-69b79e148c33", "toSide": "left", "toEnd": "arrow", "label": "Initiates SSH"}, {"id": "6c088d5e-f3db-4d00-95f9-793e6484e2f2", "fromNode": "85b26ae0-76a3-48d9-b23a-91b642a62425", "fromSide": "right", "toNode": "75489726-902b-4c02-952f-fc77abb61a72", "toSide": "left", "toEnd": "arrow", "label": "Reads level data"}, {"id": "1a1e04bb-994f-4163-b47b-efa970ce68dc", "fromNode": "961f7686-96e9-4cd3-9511-16c42dfcb6a8", "fromSide": "right", "toNode": "f6a874bb-e2a2-4f8f-8867-d79616b621b2", "toSide": "left", "toEnd": "arrow", "label": "Accesses UI"}]}, "title": "BanditGUI App | Python, Flask"}, {"type": "nested-canvas", "id": "6b2cc32d-83e3-4e6d-bb39-083b322b727d", "x": 477, "y": 138, "width": 420, "height": 180, "canvas": {"nodes": [{"type": "text", "id": "5ba9fa11-3cd7-413f-bb24-bad8c8810c26", "x": 388, "y": 30, "width": 228, "height": 108, "color": "6", "text": "\n---\nfiles: [\"venv/Lib/site-packages/litellm/main.py\"]\n---\n\n \n ## <center>LLM Orchestrator</center>  \n  <center>Python LiteLLM</center> "}, {"type": "text", "id": "07bc172b-5cfb-4030-9b71-213d81f0ea2e", "x": 766, "y": 12, "width": 172, "height": 108, "color": "6", "text": "\n---\nfiles: [\"venv/Lib/site-packages/litellm/router.py\"]\n---\n\n \n ## <center>LLM Router</center>  \n  <center>Python LiteLLM</center> "}, {"type": "text", "id": "1d3ce7c6-485c-4b61-ba43-09199af3ea92", "x": 766, "y": 170, "width": 206, "height": 108, "color": "6", "text": "\n---\nfiles: [\"venv/Lib/site-packages/litellm/cost_calculator.py\"]\n---\n\n \n ## <center>Cost Calculator</center>  \n  <center>Python LiteLLM</center> "}, {"type": "text", "id": "dbfb3799-53b6-492f-8e0e-9aeca22a789e", "x": 41, "y": 170, "width": 197, "height": 108, "color": "1", "text": "\n---\nfiles: [\"banditgui/chat/chat_manager.py\"]\n---\n\n \n ## <center>Chat Manager</center>  \n  <center>Python</center> "}, {"type": "text", "id": "7e15f5fd-9c8b-42b9-ba34-97fa529ee51a", "x": 12, "y": 12, "width": 226, "height": 108, "color": "1", "text": "\n---\nfiles: [\"banditgui/utils/api_key_manager.py\"]\n---\n\n \n ## <center>API Key Manager</center>  \n  <center>Python</center> "}, {"type": "text", "id": "fe59e397-03bd-469b-9154-115c633d0d0e", "x": 431.0000018258379, "y": 297.9999942451482, "width": 106, "height": 108, "color": "6", "text": "## <center>User</center>  \n <center>Player</center> "}, {"type": "nested-canvas", "id": "b712bebb-d51a-4a8b-8b38-c8b33dd3c84f", "x": 837.0000018258379, "y": 252.99999424514817, "width": 420, "height": 180, "canvas": {"nodes": [{"type": "text", "id": "14142887-0775-453a-bc37-88d48bdf0862", "x": 335, "y": 30, "width": 199, "height": 108, "color": "6", "text": "\n---\nfiles: [\"banditgui/static/js/bandit-app.js\",\"banditgui/static/css/style.css\",\"banditgui/templates/index.html\"]\n---\n\n \n ## <center>Frontend App</center>  \n  <center>JavaScript, HTML, CSS</center> "}, {"type": "text", "id": "e2d9b63a-b6a9-4093-af30-7cd8dee06671", "x": 684, "y": 30, "width": 254, "height": 108, "color": "6", "text": "\n---\nfiles: [\"banditgui/static/js/xterm.js\",\"banditgui/static/js/xterm-bandit-terminal.js\"]\n---\n\n \n ## <center>Web Terminal Client</center>  \n  <center>xterm.js</center> "}, {"type": "text", "id": "6c4b889f-4d24-4d9e-8e80-6571671e167c", "x": 79, "y": 170, "width": 106, "height": 108, "color": "1", "text": "## <center>User</center>  \n <center>Player</center> "}, {"type": "text", "id": "a09e2404-d773-416f-a1cb-665ff3be8b78", "x": 12, "y": 12, "width": 173, "height": 108, "color": "1", "text": "\n---\nfiles: [\"banditgui/app.py\",\"venv/Lib/site-packages/flask\",\"venv/Lib/site-packages/werkzeug\"]\n---\n\n \n ## <center>Web Server</center>  \n  <center>Flask, Werkzeug</center> "}], "edges": [{"id": "8a856a92-1f74-4bd9-a6fb-8a7713f3c52b", "fromNode": "14142887-0775-453a-bc37-88d48bdf0862", "fromSide": "right", "toNode": "e2d9b63a-b6a9-4093-af30-7cd8dee06671", "toSide": "left", "toEnd": "arrow", "label": "Embeds"}, {"id": "3a2fb25f-71c2-4724-9434-24cfb39371ef", "fromNode": "6c4b889f-4d24-4d9e-8e80-6571671e167c", "fromSide": "right", "toNode": "14142887-0775-453a-bc37-88d48bdf0862", "toSide": "left", "toEnd": "arrow", "label": "Uses"}, {"id": "9b313440-9d4c-40d1-9419-b126cae1acc4", "fromNode": "a09e2404-d773-416f-a1cb-665ff3be8b78", "fromSide": "right", "toNode": "14142887-0775-453a-bc37-88d48bdf0862", "toSide": "left", "toEnd": "arrow", "label": "Serves content"}]}, "title": "User Web Browser | Web Browser (e.g., Chrome)"}, {"type": "nested-canvas", "id": "0f54ede6-ec51-4414-9420-414274c208bc", "x": 117.0000018258379, "y": 67.99999424514817, "width": 420, "height": 180, "canvas": {"nodes": [{"type": "text", "id": "eae3a689-0092-4c43-90b4-0f1495830e44", "x": 736.6666666666666, "y": 710, "width": 173, "height": 108, "color": "6", "text": "\n---\nfiles: [\"banditgui/app.py\",\"venv/Lib/site-packages/flask\",\"venv/Lib/site-packages/werkzeug\"]\n---\n\n \n ## <center>Web Server</center>  \n  <center>Flask, Werkzeug</center> "}, {"type": "text", "id": "7df17d0e-032c-4a60-9284-b3da2374da35", "x": 1101, "y": 411, "width": 223, "height": 108, "color": "6", "text": "\n---\nfiles: [\"banditgui/utils/session_manager.py\",\"user_data/sessions.json\",\"venv/Lib/site-packages/flask_session\"]\n---\n\n \n ## <center>Session Manager</center>  \n  <center>Flask-Session</center> "}, {"type": "text", "id": "70e12797-c05b-4eab-8e1d-3f27e7d48dd0", "x": 756.6666666666666, "y": 375, "width": 174, "height": 108, "color": "6", "text": "\n---\nfiles: [\"banditgui/utils/progression_manager.py\",\"banditgui/models/progression.py\",\"banditgui/data/levels_info.json\"]\n---\n\n \n ## <center>Game Logic</center>  \n  <center>Python</center> "}, {"type": "text", "id": "ed217106-450a-476f-a2da-f78bf0fad689", "x": 1101, "y": 253, "width": 215, "height": 108, "color": "6", "text": "\n---\nfiles: [\"banditgui/chat/chat_manager.py\",\"venv/Lib/site-packages/litellm\",\"venv/Lib/site-packages/openai\"]\n---\n\n \n ## <center>LLM Integration</center>  \n  <center>Python, LiteLLM</center> "}, {"type": "text", "id": "4a61dde0-deca-4bfb-886a-71ce7d5e517d", "x": 1101, "y": 48, "width": 171, "height": 108, "color": "6", "text": "\n---\nfiles: [\"banditgui/ssh/ssh_manager.py\",\"venv/Lib/site-packages/paramiko\"]\n---\n\n \n ## <center>SSH Client</center>  \n  <center>Python, Paramiko</center> "}, {"type": "text", "id": "677c322c-2afa-45e7-9b8d-44ab9e6a9f33", "x": 716, "y": 30, "width": 235, "height": 108, "color": "6", "text": "\n---\nfiles: [\"banditgui/terminal/terminal_manager.py\"]\n---\n\n \n ## <center>Terminal Interface</center>  \n  <center>Python</center> "}, {"type": "text", "id": "b2c3bedf-adb3-4dc5-9986-e7ab14cc2cc5", "x": 1101, "y": 598, "width": 263, "height": 108, "color": "6", "text": "\n---\nfiles: [\"banditgui/utils/api_key_manager.py\",\"banditgui/utils/csrf.py\",\"banditgui/utils/validation.py\",\"venv/Lib/site-packages/pydantic\"]\n---\n\n \n ## <center>Security & Validation</center>  \n  <center>Python, PyDantic</center> "}, {"type": "text", "id": "c0a83875-ad5c-43b7-ace7-62877d52c500", "x": 50, "y": 186, "width": 216, "height": 108, "color": "6", "text": "\n---\nfiles: [\"banditgui/config/settings.py\",\"banditgui/config/llm_model.json\",\"banditgui/.env\"]\n---\n\n \n ## <center>Config Manager</center>  \n  <center>Python</center> "}, {"type": "text", "id": "fa78a456-1757-4ab9-9426-ed6dec8280ed", "x": 67, "y": 728, "width": 199, "height": 108, "color": "1", "text": "\n---\nfiles: [\"banditgui/static/js/bandit-app.js\",\"banditgui/static/css/style.css\",\"banditgui/templates/index.html\"]\n---\n\n \n ## <center>Frontend App</center>  \n  <center>JavaScript, HTML, CSS</center> "}, {"type": "text", "id": "b9b54234-b418-4f24-b04b-45fb3283398d", "x": 12, "y": 12, "width": 254, "height": 108, "color": "1", "text": "\n---\nfiles: [\"banditgui/static/js/xterm.js\",\"banditgui/static/js/xterm-bandit-terminal.js\"]\n---\n\n \n ## <center>Web Terminal Client</center>  \n  <center>xterm.js</center> "}], "edges": [{"id": "9b3a0fd3-416a-477d-ae2c-1c9cef326770", "fromNode": "677c322c-2afa-45e7-9b8d-44ab9e6a9f33", "fromSide": "right", "toNode": "4a61dde0-deca-4bfb-886a-71ce7d5e517d", "toSide": "left", "toEnd": "arrow", "label": "Sends commands"}, {"id": "ce24c0d8-8b16-4929-94d2-9d7015e84b06", "fromNode": "70e12797-c05b-4eab-8e1d-3f27e7d48dd0", "fromSide": "right", "toNode": "7df17d0e-032c-4a60-9284-b3da2374da35", "toSide": "left", "toEnd": "arrow", "label": "Manages state"}, {"id": "57615fa7-7cd0-4ebc-aec3-4de1cbdd2f36", "fromNode": "70e12797-c05b-4eab-8e1d-3f27e7d48dd0", "fromSide": "right", "toNode": "ed217106-450a-476f-a2da-f78bf0fad689", "toSide": "left", "toEnd": "arrow", "label": "Requests hints"}, {"id": "87e4c4ee-18e9-4408-8fec-1d056b8ad5e8", "fromNode": "eae3a689-0092-4c43-90b4-0f1495830e44", "fromSide": "right", "toNode": "b2c3bedf-adb3-4dc5-9986-e7ab14cc2cc5", "toSide": "left", "toEnd": "arrow", "label": "Validates requests"}, {"id": "6a902153-76ce-4e37-b2be-d04bcd5b12a7", "fromNode": "c0a83875-ad5c-43b7-ace7-62877d52c500", "fromSide": "right", "toNode": "eae3a689-0092-4c43-90b4-0f1495830e44", "toSide": "left", "toEnd": "arrow", "label": "Provides settings"}, {"id": "dbd41880-9eea-4603-bcc7-44b4a0eafc07", "fromNode": "c0a83875-ad5c-43b7-ace7-62877d52c500", "fromSide": "right", "toNode": "7df17d0e-032c-4a60-9284-b3da2374da35", "toSide": "left", "toEnd": "arrow", "label": "Provides settings"}, {"id": "ad744122-226e-4a1e-9f51-2ec47ce67c58", "fromNode": "c0a83875-ad5c-43b7-ace7-62877d52c500", "fromSide": "right", "toNode": "70e12797-c05b-4eab-8e1d-3f27e7d48dd0", "toSide": "left", "toEnd": "arrow", "label": "Provides settings"}, {"id": "2629b9c8-eab8-4176-ac0d-8ea49f38f4aa", "fromNode": "c0a83875-ad5c-43b7-ace7-62877d52c500", "fromSide": "right", "toNode": "ed217106-450a-476f-a2da-f78bf0fad689", "toSide": "left", "toEnd": "arrow", "label": "Provides settings"}, {"id": "bb442a93-3919-49aa-989c-8dfa75f60b98", "fromNode": "c0a83875-ad5c-43b7-ace7-62877d52c500", "fromSide": "right", "toNode": "4a61dde0-deca-4bfb-886a-71ce7d5e517d", "toSide": "left", "toEnd": "arrow", "label": "Provides settings"}, {"id": "1a9f5442-bb07-48e5-ace0-931f3b970068", "fromNode": "c0a83875-ad5c-43b7-ace7-62877d52c500", "fromSide": "right", "toNode": "677c322c-2afa-45e7-9b8d-44ab9e6a9f33", "toSide": "left", "toEnd": "arrow", "label": "Provides settings"}, {"id": "7e549c9a-be9a-4f5b-a82a-0f95cf0f9335", "fromNode": "c0a83875-ad5c-43b7-ace7-62877d52c500", "fromSide": "right", "toNode": "b2c3bedf-adb3-4dc5-9986-e7ab14cc2cc5", "toSide": "left", "toEnd": "arrow", "label": "Provides settings"}, {"id": "b12c6b9f-d862-474f-a8d4-603d1c55c12c", "fromNode": "fa78a456-1757-4ab9-9426-ed6dec8280ed", "fromSide": "right", "toNode": "eae3a689-0092-4c43-90b4-0f1495830e44", "toSide": "left", "toEnd": "arrow", "label": "Sends requests"}, {"id": "a7d8f988-68aa-40c3-905c-f0d0e57d6dc4", "fromNode": "b9b54234-b418-4f24-b04b-45fb3283398d", "fromSide": "right", "toNode": "677c322c-2afa-45e7-9b8d-44ab9e6a9f33", "toSide": "left", "toEnd": "arrow", "label": "Communicates"}]}, "title": "BanditGUI App | Python, Flask"}, {"type": "nested-canvas", "id": "78460d93-7a39-46c7-8388-e29ab80b79a1", "x": 837.0000018258379, "y": 22.999994245148173, "width": 420, "height": 180, "canvas": {"nodes": [{"type": "text", "id": "2bb0ab09-9525-4b89-95cc-7a0617ba0734", "x": 385, "y": 804, "width": 195, "height": 108, "color": "6", "text": "\n---\nfiles: []\n---\n\n \n ## <center>App Database</center>  \n  <center>JSON Filesystem</center> "}, {"type": "text", "id": "119eab31-aa41-448f-90b5-3148a361bbc5", "x": 333, "y": 12, "width": 193, "height": 108, "color": "6", "text": "\n---\nfiles: []\n---\n\n \n ## <center>Game Servers</center>  \n  <center>Linux SSH Servers</center> "}, {"type": "text", "id": "0af89a27-d4b3-41e6-b461-a0f5b582c124", "x": 377, "y": 528, "width": 264, "height": 108, "color": "6", "text": "\n---\nfiles: []\n---\n\n \n ## <center>LLM Provider</center>  \n  <center>External AI Service (e.g., OpenAI)</center> "}, {"type": "text", "id": "4e703557-fbd6-4d93-8b60-e1dcd8b7e116", "x": 425, "y": 270, "width": 214, "height": 108, "color": "6", "text": "\n---\nfiles: []\n---\n\n \n ## <center>Logging System</center>  \n  <center>Filesystem Logging</center> "}, {"type": "text", "id": "b8633e90-b688-4256-99e2-f2ef27bcb054", "x": 12, "y": 12, "width": 171, "height": 108, "color": "1", "text": "\n---\nfiles: [\"banditgui/ssh/ssh_manager.py\",\"venv/Lib/site-packages/paramiko\"]\n---\n\n \n ## <center>SSH Client</center>  \n  <center>Python, Paramiko</center> "}, {"type": "text", "id": "958cd163-52c5-49b3-8eb4-8b264cb19168", "x": 12, "y": 528, "width": 215, "height": 108, "color": "1", "text": "\n---\nfiles: [\"banditgui/chat/chat_manager.py\",\"venv/Lib/site-packages/litellm\",\"venv/Lib/site-packages/openai\"]\n---\n\n \n ## <center>LLM Integration</center>  \n  <center>Python, LiteLLM</center> "}, {"type": "text", "id": "0a1bc19f-b605-4e2d-8a9e-fa745a8b5cff", "x": 12, "y": 944, "width": 223, "height": 108, "color": "1", "text": "\n---\nfiles: [\"banditgui/utils/session_manager.py\",\"user_data/sessions.json\",\"venv/Lib/site-packages/flask_session\"]\n---\n\n \n ## <center>Session Manager</center>  \n  <center>Flask-Session</center> "}, {"type": "text", "id": "81b7cc9d-967c-426e-884b-40c07dc3d487", "x": 61, "y": 786, "width": 174, "height": 108, "color": "1", "text": "\n---\nfiles: [\"banditgui/utils/progression_manager.py\",\"banditgui/models/progression.py\",\"banditgui/data/levels_info.json\"]\n---\n\n \n ## <center>Game Logic</center>  \n  <center>Python</center> "}, {"type": "text", "id": "891f38fc-c6bb-4566-a4fb-d3c10ce49757", "x": 12, "y": 270, "width": 263, "height": 108, "color": "1", "text": "\n---\nfiles: [\"banditgui/utils/api_key_manager.py\",\"banditgui/utils/csrf.py\",\"banditgui/utils/validation.py\",\"venv/Lib/site-packages/pydantic\"]\n---\n\n \n ## <center>Security & Validation</center>  \n  <center>Python, PyDantic</center> "}], "edges": [{"id": "79266e3c-2d3d-4af2-86e8-b2013f26961b", "fromNode": "b8633e90-b688-4256-99e2-f2ef27bcb054", "fromSide": "right", "toNode": "119eab31-aa41-448f-90b5-3148a361bbc5", "toSide": "left", "toEnd": "arrow", "label": "Connects SSH"}, {"id": "c4fd1560-b611-496b-8578-9f0393591359", "fromNode": "958cd163-52c5-49b3-8eb4-8b264cb19168", "fromSide": "right", "toNode": "0af89a27-d4b3-41e6-b461-a0f5b582c124", "toSide": "left", "toEnd": "arrow", "label": "Calls API"}, {"id": "a0e4f7de-bd76-449f-a13a-bc5b53140bea", "fromNode": "0a1bc19f-b605-4e2d-8a9e-fa745a8b5cff", "fromSide": "right", "toNode": "2bb0ab09-9525-4b89-95cc-7a0617ba0734", "toSide": "left", "toEnd": "arrow", "label": "Reads/writes"}, {"id": "57f05790-2861-4a59-9dd3-5b040a9b3679", "fromNode": "81b7cc9d-967c-426e-884b-40c07dc3d487", "fromSide": "right", "toNode": "2bb0ab09-9525-4b89-95cc-7a0617ba0734", "toSide": "left", "toEnd": "arrow", "label": "Reads/writes"}, {"id": "12b9bfe1-064d-4fd1-bb9c-09edd765d7b6", "fromNode": "891f38fc-c6bb-4566-a4fb-d3c10ce49757", "fromSide": "right", "toNode": "4e703557-fbd6-4d93-8b60-e1dcd8b7e116", "toSide": "left", "toEnd": "arrow", "label": "Sends logs"}]}, "title": "External Systems | Various"}], "edges": [{"id": "36734006-be87-4d63-ba10-bf4b6a8efc89", "fromNode": "5ba9fa11-3cd7-413f-bb24-bad8c8810c26", "fromSide": "right", "toNode": "07bc172b-5cfb-4030-9b71-213d81f0ea2e", "toSide": "left", "toEnd": "arrow", "label": "Routes requests"}, {"id": "ee6fe850-0081-48bb-aa80-0fb2bfea0668", "fromNode": "5ba9fa11-3cd7-413f-bb24-bad8c8810c26", "fromSide": "right", "toNode": "1d3ce7c6-485c-4b61-ba43-09199af3ea92", "toSide": "left", "toEnd": "arrow", "label": "Tracks cost"}, {"id": "c198946c-bfca-4581-b6d0-ee33b085b29b", "fromNode": "dbfb3799-53b6-492f-8e0e-9aeca22a789e", "fromSide": "right", "toNode": "5ba9fa11-3cd7-413f-bb24-bad8c8810c26", "toSide": "left", "toEnd": "arrow", "label": "Sends prompts"}, {"id": "267b5409-c411-48c1-b4a3-e2ad96a7ffb0", "fromNode": "7e15f5fd-9c8b-42b9-ba34-97fa529ee51a", "fromSide": "right", "toNode": "5ba9fa11-3cd7-413f-bb24-bad8c8810c26", "toSide": "left", "toEnd": "arrow", "label": "Provides keys"}, {"id": "3a2fb25f-71c2-4724-9434-24cfb39371ef", "fromNode": "fe59e397-03bd-469b-9154-115c633d0d0e", "fromSide": "right", "toNode": "b712bebb-d51a-4a8b-8b38-c8b33dd3c84f", "toSide": "left", "toEnd": "arrow", "label": "Uses"}, {"id": "b12c6b9f-d862-474f-a8d4-603d1c55c12c", "fromNode": "b712bebb-d51a-4a8b-8b38-c8b33dd3c84f", "fromSide": "left", "toNode": "0f54ede6-ec51-4414-9420-414274c208bc", "toSide": "right", "toEnd": "arrow", "label": "Sends requests"}, {"id": "9b313440-9d4c-40d1-9419-b126cae1acc4", "fromNode": "0f54ede6-ec51-4414-9420-414274c208bc", "fromSide": "right", "toNode": "b712bebb-d51a-4a8b-8b38-c8b33dd3c84f", "toSide": "left", "toEnd": "arrow", "label": "Serves content"}, {"id": "79266e3c-2d3d-4af2-86e8-b2013f26961b", "fromNode": "0f54ede6-ec51-4414-9420-414274c208bc", "fromSide": "right", "toNode": "78460d93-7a39-46c7-8388-e29ab80b79a1", "toSide": "left", "toEnd": "arrow", "label": "Connects SSH"}]}, "title": "LLM Proxy | Python, LiteLLM"}], "edges": [{"id": "1a1e04bb-994f-4163-b47b-efa970ce68dc", "fromNode": "a3b5797d-d03b-4e0c-b4d7-ddb89328c5e0", "fromSide": "right", "toNode": "fecfa567-e2e1-4896-a48c-ed86a5e9af71", "toSide": "left", "toEnd": "arrow", "label": "Accesses UI"}, {"id": "c198946c-bfca-4581-b6d0-ee33b085b29b", "fromNode": "fecfa567-e2e1-4896-a48c-ed86a5e9af71", "fromSide": "right", "toNode": "6b2cc32d-83e3-4e6d-bb39-083b322b727d", "toSide": "left", "toEnd": "arrow", "label": "Sends prompts"}]}