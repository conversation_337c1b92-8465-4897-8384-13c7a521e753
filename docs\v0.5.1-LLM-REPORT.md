# Codebase Research Report

## Query
llm feature details

## Answer
# LLM Feature Details

## High-Level Overview

```mermaid
graph TB
  user["User<br>Actor<br>N/A"]
  banditGUI["BanditGUI<br>Application<br>banditgui/"]
  llmFeature["LLM Feature<br>Functionality<br>banditgui/app.py"]
  litellmLib["litellm Library<br>External Dependency<br>N/A"]
  llmProvider["LLM Provider<br>External Service<br>N/A"]
  configJson["Configuration File<br>JSON<br>banditgui/config/llm_model.json"]

  user --> |"requests advice"| banditGUI
  banditGUI --> |"uses"| llmFeature
  llmFeature --> |"reads config"| configJson
  llmFeature --> |"integrates with"| litellmLib
  litellmLib --> |"communicates with"| llmProvider
  llmProvider --> |"returns advice"| litellmLib
  litellmLib --> |"sends response"| llmFeature
  llmFeature --> |"returns advice"| banditG<PERSON>
```

The LLM (Large Language Model) feature in BanditGUI provides an "Ask-a-Pro" functionality, allowing users to get advice from a configured LLM. This feature integrates with various LLM providers through the `litellm` library. The selection and configuration of the LLM model are managed via a JSON configuration file.

## Mid-Level Architecture

```mermaid
graph TB
  frontend["Frontend UI<br>JavaScript<br>banditgui/static/js/bandit-app.js"]
  appPy["banditgui/app.py<br>Flask Backend<br>banditgui/app.py"]
  llmModelJson["llm_model.json<br>Configuration<br>banditgui/config/llm_model.json"]
  litellm["litellm<br>Python Library<br>N/A"]
  llmProviders["LLM Providers<br>External Service<br>N/A"]
  testAppPy["test_app.py<br>Unit Tests<br>banditgui/tests/test_app.py"]

  frontend --> |"requests /ask-a-pro"| appPy
  frontend --> |"fetches /config/llm_model.json"| appPy
  appPy --> |"reads"| llmModelJson
  appPy --> |"calls completion"| litellm
  litellm --> |"API call"| llmProviders
  llmProviders --> |"response"| litellm
  litellm --> |"returns result"| appPy
  appPy --> |"sends JSON response"| frontend
  testAppPy --> |"tests endpoints"| appPy
  testAppPy --> |"verifies config serving"| llmModelJson
```


### **`banditgui/app.py`**
This is the core backend component responsible for handling LLM requests.
*   **Purpose**: Manages the "Ask-a-Pro" endpoint, processes user prompts, interacts with the `litellm` library, and returns LLM-generated advice.
*   **Key Internal Parts**:
    *   `ask_a_pro()` function: The Flask route that receives user requests, reads the selected LLM model from `llm_model.json`, constructs the prompt, calls `litellm.completion`, and returns the LLM's response.
    *   `serve_llm_model_json()` function: Serves the [llm_model.json](banditgui/config/llm_model.json) file to the frontend.
*   **External Relationships**:
    *   **Reads from**: [banditgui/config/llm_model.json](banditgui/config/llm_model.json) to determine the active LLM model and its provider.
    *   **Interacts with**: `litellm` library for making API calls to various LLM providers (e.g., OpenAI, Ollama, Cohere).
    *   **Receives requests from**: Frontend (via `/ask-a-pro` endpoint).
    *   **Sends responses to**: Frontend (JSON containing LLM advice or error messages).

### **`banditgui/config/llm_model.json`**
This JSON file acts as the configuration for available LLM models.
*   **Purpose**: Defines a list of LLM providers and their respective models that can be used by the "Ask-a-Pro" feature.
*   **Structure**: A dictionary where keys are LLM providers (e.g., "openai", "ollama", "cohere") and values are arrays of model names.
*   **Example Content**:
    ```json
    {
        "openai": ["gpt-4o", "gpt-3.5-turbo"],
        "ollama": ["qwen2.5-1.5b"],
        "cohere": ["command-r-plus"],
        "openrouter": ["nous-hermes-2"]
    }
    ```

### **`banditgui/static/js/bandit-app.js`**
This JavaScript file handles the frontend interaction for the LLM feature.
*   **Purpose**: Populates the LLM selection dropdown in the UI and sends "Ask-a-Pro" requests to the backend.
*   **Key Internal Parts**:
    *   `populateLlmDropdown()`: Fetches the [llm_model.json](banditgui/config/llm_model.json) from the backend and dynamically populates a dropdown menu with available LLM models.
    *   Event listeners for the "Ask-a-Pro" button: Triggers an AJAX request to the `/ask-a-pro` endpoint with the selected LLM model and user prompt.
*   **External Relationships**:
    *   **Fetches from**: `/config/llm_model.json` endpoint (served by [banditgui/app.py](banditgui/app.py)).
    *   **Sends requests to**: `/ask-a-pro` endpoint (handled by [banditgui/app.py](banditgui/app.py)).

### **`banditgui/tests/test_app.py`**
This file contains unit tests for the LLM feature's backend logic.
*   **Purpose**: Ensures the correct functioning of the `/config/llm_model.json` serving and the `/ask-a-pro` endpoint, including error handling and different LLM provider integrations.
*   **Key Test Cases**:
    *   `test_serve_llm_model_json_success()`: Verifies that [llm_model.json](banditgui/config/llm_model.json) is served correctly.
    *   `test_serve_llm_model_json_not_found()`: Tests the behavior when [llm_model.json](banditgui/config/llm_model.json) is not found.
    *   `test_ask_a_pro_openai_success()`, `test_ask_a_pro_ollama_success()`, `test_ask_a_pro_cohere_success()`, `test_ask_a_pro_openrouter_success()`: Test successful LLM calls for various providers.
    *   `test_ask_a_pro_litellm_completion_error()`: Tests error handling when `litellm.completion` fails.
    *   `test_ask_a_pro_litellm_not_installed_error()`: Tests error handling when `litellm` is not installed.

## Low-Level Implementation Details

```mermaid
graph TB
  request["User Request<br>HTTP<br>N/A"]
  askAProFunc["ask_a_pro()<br>Flask Function<br>banditgui/app.py:L209"]
  selectedLlmValue["selected_llm_value<br>Request Data<br>banditgui/app.py:L221"]
  modelNameProvider["model_name_for_api, provider<br>Variables<br>banditgui/app.py:L221"]
  litellmCompletion["litellm.completion()<br>Function Call<br>banditgui/app.py:L257"]
  llmResponse["LLM Response<br>Data<br>banditgui/app.py:L263"]
  errorHandling["Error Handling<br>Try-Except Block<br>banditgui/app.py:L275"]
  populateDropdown["populateLlmDropdown()<br>JavaScript Function<br>banditgui/static/js/bandit-app.js:L43"]
  fetchConfig["Fetch /config/llm_model.json<br>AJAX Call<br>banditgui/static/js/bandit-app.js:L51"]
  sendSelectedLlm["Send selectedLlm<br>Request Payload<br>banditgui/static/js/bandit-app.js:L237"]
  testLitellmError["Test litellm Error<br>Test Case<br>banditgui/tests/test_app.py:L176"]

  request --> |"contains prompt"| askAProFunc
  askAProFunc --> |"extracts"| selectedLlmValue
  selectedLlmValue --> |"determines"| modelNameProvider
  modelNameProvider --> |"used in"| litellmCompletion
  litellmCompletion --> |"returns"| llmResponse
  llmResponse --> |"processed by"| askAProFunc
  askAProFunc --> |"implements"| errorHandling
  populateDropdown --> |"initiates"| fetchConfig
  fetchConfig --> |"populates UI"| populateDropdown
  askAProFunc --> |"receives"| sendSelectedLlm
  testLitellmError --> |"verifies"| errorHandling
```


### LLM Model Selection and Usage
*   The `selected_llm_value` is extracted from the request and used to determine the `model_name_for_api` and `provider` for `litellm`.
    *   [banditgui/app.py](banditgui/app.py:L209)
    *   [banditgui/app.py](banditgui/app.py:L221)
*   The `litellm.completion` function is called with the appropriate model and messages.
    *   [banditgui/app.py](banditgui/app.py:L257)
    *   [banditgui/app.py](banditgui/app.py:L263)

### Frontend Interaction
*   The LLM dropdown is initialized and populated by calling `populateLlmDropdown()`.
    *   [banditgui/static/js/bandit-app.js](banditgui/static/js/bandit-app.js:L43)
*   The `populateLlmDropdown()` function fetches the LLM models from `/config/llm_model.json`.
    *   [banditgui/static/js/bandit-app.js](banditgui/static/js/bandit-app.js:L51)
*   When the "Ask-a-Pro" button is clicked, the `selectedLlm` value from the dropdown is sent in the request payload.
    *   [banditgui/static/js/bandit-app.js](banditgui/static/js/bandit-app.js:L213)
    *   [banditgui/static/js/bandit-app.js](banditgui/static/js/bandit-app.js:L237)

### Error Handling
*   The backend includes `try-except` blocks to catch errors during LLM calls and provides informative error messages to the frontend.
    *   [banditgui/app.py](banditgui/app.py:L275)
    *   [banditgui/app.py](banditgui/app.py:L282)
*   Tests specifically cover scenarios where `litellm` is not installed or encounters an error.
    *   [banditgui/tests/test_app.py](banditgui/tests/test_app.py:L176)
    *   [banditgui/tests/test_app.py](banditgui/tests/test_app.py:L196)I have already provided the LLM feature details in a structured Markdown report in my previous response. Please let me know if you have any further questions or if there's anything else I can help you with regarding the LLM feature or any other part of the codebase.

---
*Generated by [CodeViz.ai](https://codeviz.ai) on 7/7/2025, 12:37:29 PM*
