{"name": "banditgui", "version": "0.5.0", "description": "A web-based interface for the OverTheWire Bandit wargame", "main": "index.js", "scripts": {"build": "webpack", "start": "python -m banditgui.app"}, "dependencies": {"claude-code": "^0.0.2", "playwright": "^1.53.1", "xterm": "^4.19.0", "xterm-addon-fit": "^0.5.0", "xterm-addon-web-links": "^0.4.0"}, "directories": {"doc": "docs"}, "devDependencies": {"@babel/core": "^7.27.7", "@babel/preset-env": "^7.27.2", "babel-loader": "^10.0.0", "css-loader": "^7.1.2", "mini-css-extract-plugin": "^2.9.2", "style-loader": "^4.0.0", "webpack": "^5.99.9", "webpack-cli": "^6.0.1"}, "keywords": ["overthewire", "bandit", "wargames", "terminal", "ssh", "learning", "education", "command", "security", "ai", "assistant"], "author": "<PERSON> <<EMAIL>>", "license": "ISC"}