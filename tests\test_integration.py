import os
import unittest
from unittest.mock import patch

from banditgui.app import app as flask_app
from banditgui.utils.progression_manager import ProgressionManager


class TestSessionIntegration(unittest.TestCase):
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.app = flask_app
        self.app.config['SECRET_KEY'] = 'test_secret_key'
        self.app.config['SESSION_COOKIE_SECURE'] = True
        self.app.config['SESSION_COOKIE_HTTPONLY'] = True
        self.app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
        self.app.config['PERMANENT_SESSION_LIFETIME'] = 30 * 60  # 30 minutes
        self.app.config['SESSION_REFRESH_EACH_REQUEST'] = True
        self.app.config['SESSION_TYPE'] = 'filesystem'
        self.client = self.app.test_client()
        self.manager = ProgressionManager(data_dir="test_data_integration")

    def tearDown(self):
        """Clean up after each test method."""
        import shutil
        if os.path.exists("test_data_integration"):
            shutil.rmtree("test_data_integration")

    def test_complete_session_lifecycle(self):
        """Test complete session lifecycle."""
        with self.client as c:
            # Start session
            response = c.get('/')
            self.assertEqual(response.status_code, 200)
            # Get CSRF token
            resp = c.get('/test-csrf-token')
            csrf_token = resp.get_json()['csrf_token']
            # Update level progress
            response = c.post('/progress/update', json={
                'csrf_token': csrf_token,
                'level': 1,
                'command': 'ls -la',
                'output': 'test output'
            })
            self.assertEqual(response.status_code, 200)
            # Complete level
            response = c.post('/complete-level', json={
                'csrf_token': csrf_token,
                'level': 1,
                'password': 'test_password',
                'time_taken': 120.5
            })
            self.assertEqual(response.status_code, 200)
            # Get progress
            response = c.get('/progress/stats')
            self.assertEqual(response.status_code, 200)
            progress = response.get_json()
            self.assertEqual(progress['current_level'], 2)
            self.assertEqual(progress['total_levels_completed'], 1)

    def test_session_timeout(self):
        """Test session timeout and refresh."""
        with self.client as c:
            # Start session
            response = c.get('/')
            self.assertEqual(response.status_code, 200)
            # Wait longer than session timeout
            import time
            time.sleep(31)  # Wait 31 seconds
            # Get CSRF token (simulate new session)
            resp = c.get('/test-csrf-token')
            csrf_token = resp.get_json()['csrf_token']
            # Try to update progress (should fail due to timeout)
            response = c.post('/progress/update', json={
                'csrf_token': csrf_token,
                'level': 1,
                'command': 'ls -la',
                'output': 'test output'
            })
            self.assertEqual(response.status_code, 400)

    def test_rate_limiting(self):
        """Test rate limiting across multiple endpoints."""
        with self.client as c:
            resp = c.get('/test-csrf-token')
            csrf_token = resp.get_json()['csrf_token']
            for i in range(4):  # 4 requests should hit rate limit
                response = c.post('/ask-a-pro', json={
                    'csrf_token': csrf_token,
                    'llm': 'openai/gpt-4',
                    'level_name': 1,
                    'level_description': 'Test level',
                    'command_history': ['ls', 'pwd']
                })
                if i < 3:
                    self.assertEqual(response.status_code, 400)  # Missing API key
                else:
                    self.assertEqual(response.status_code, 429)

    def test_api_key_rotation(self):
        """Test API key rotation and validation."""
        with self.app.test_request_context('/'):
            # Test with valid API key
            with patch.dict('os.environ', {'OPENAI_API_KEY': 'test_key_1'}):
                self.assertEqual(os.getenv('OPENAI_API_KEY'), 'test_key_1')
                
            # Test with rotated API key
            with patch.dict('os.environ', {'OPENAI_API_KEY': 'test_key_2'}):
                self.assertEqual(os.getenv('OPENAI_API_KEY'), 'test_key_2')

    def test_command_injection_protection(self):
        """Test protection against command injection."""
        with self.client as c:
            resp = c.get('/test-csrf-token')
            csrf_token = resp.get_json()['csrf_token']
            response = c.post('/progress/update', json={
                'csrf_token': csrf_token,
                'level': 1,
                'command': '; rm -rf /',  # Malicious command
                'output': 'test output'
            })
            self.assertEqual(response.status_code, 400)  # Should be rejected

    def test_multiple_sessions(self):
        """Test handling of multiple concurrent sessions."""
        # Session 1
        c1 = self.app.test_client()
        c1.get('/')
        resp1 = c1.get('/test-csrf-token')
        csrf_token_1 = resp1.get_json()['csrf_token']
        # Session 2
        c2 = self.app.test_client()
        c2.get('/')
        resp2 = c2.get('/test-csrf-token')
        csrf_token_2 = resp2.get_json()['csrf_token']
        # Update progress in session 1
        response = c1.post('/progress/update', json={
            'csrf_token': csrf_token_1,
            'level': 1,
            'command': 'ls -la',
            'output': 'test output'
        })
        self.assertEqual(response.status_code, 200)
        # Update progress in session 2
        response = c2.post('/progress/update', json={
            'csrf_token': csrf_token_2,
            'level': 1,
            'command': 'ls -la',
            'output': 'test output'
        })
        self.assertEqual(response.status_code, 200)

if __name__ == '__main__':
    unittest.main()
