import unittest
from unittest.mock import patch

from banditgui.app import app as flask_app
from banditgui.models.progression import PlayerSession, PlayerStats
from banditgui.utils.progression_manager import (
    InvalidLevelError,
    ProgressionManager,
    SessionNotFoundError,
)


class TestProgressionManager(unittest.TestCase):
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.manager = ProgressionManager(data_dir="test_data")
        self.test_session_id = "test_session_123"
        self.test_level = 1
        self.test_command = "ls -la"
        self.test_output = "test output"
        self.test_time_taken = 120.5
        self.app = flask_app
        self.app.config['SECRET_KEY'] = 'test_secret_key'
        self.app.config['SESSION_COOKIE_SECURE'] = True
        self.app.config['SESSION_COOKIE_HTTPONLY'] = True
        self.app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
        self.app.config['PERMANENT_SESSION_LIFETIME'] = 30 * 60  # 30 minutes
        self.app.config['SESSION_REFRESH_EACH_REQUEST'] = True
        self.app.config['TESTING'] = True
        self.app.config['SESSION_TYPE'] = 'filesystem'

    def tearDown(self):
        """Clean up after each test method."""
        import os
        import shutil
        if os.path.exists("test_data"):
            shutil.rmtree("test_data")

    def test_start_session_success(self):
        """Test starting a new session."""
        session = self.manager.start_session(self.test_session_id)
        self.assertIsInstance(session, PlayerSession)
        self.assertEqual(session.session_id, self.test_session_id)
        self.assertEqual(session.current_level, 1)
        self.assertEqual(len(session.command_history), 0)

    def test_start_session_invalid_id(self):
        """Test starting a session with invalid session ID."""
        with self.assertRaises(ValueError):
            self.manager.start_session("")

    def test_update_level_progress_success(self):
        """Test updating level progress."""
        self.manager.start_session(self.test_session_id)
        self.manager.update_level_progress(
            self.test_session_id,
            self.test_level,
            self.test_command,
            self.test_output
        )
        session = self.manager.sessions[self.test_session_id]
        self.assertEqual(len(session['command_history']), 1)
        self.assertEqual(session['command_history'][0]['command'], self.test_command)

    def test_update_level_progress_invalid_level(self):
        """Test updating progress with invalid level."""
        self.manager.start_session(self.test_session_id)
        with self.assertRaises(InvalidLevelError):
            self.manager.update_level_progress(
                self.test_session_id,
                -1,
                self.test_command,
                self.test_output
            )

    def test_update_level_progress_invalid_command(self):
        """Test updating progress with invalid command."""
        self.manager.start_session(self.test_session_id)
        with self.assertRaises(ValueError):
            self.manager.update_level_progress(
                self.test_session_id,
                self.test_level,
                "",
                self.test_output
            )

    def test_complete_level_success(self):
        """Test completing a level."""
        self.manager.start_session(self.test_session_id)
        self.manager.complete_level(
            self.test_session_id,
            self.test_level,
            "test_password",
            self.test_time_taken
        )
        session = self.manager.sessions[self.test_session_id]
        self.assertIn(self.test_level, session['completed_levels'])
        self.assertEqual(session['current_level'], self.test_level + 1)
        self.assertEqual(session['time_spent'][str(self.test_level)], self.test_time_taken)

    def test_complete_level_invalid_level(self):
        """Test completing level with invalid level number."""
        self.manager.start_session(self.test_session_id)
        with self.assertRaises(InvalidLevelError):
            self.manager.complete_level(
                self.test_session_id,
                -1,
                "test_password",
                self.test_time_taken
            )

    def test_complete_level_invalid_time(self):
        """Test completing level with invalid time."""
        self.manager.start_session(self.test_session_id)
        with self.assertRaises(ValueError):
            self.manager.complete_level(
                self.test_session_id,
                self.test_level,
                "test_password",
                -1
            )

    def test_get_session_progress_success(self):
        """Test getting session progress."""
        self.manager.start_session(self.test_session_id)
        progress = self.manager.get_session_progress(self.test_session_id)
        self.assertEqual(progress['current_level'], 1)
        self.assertEqual(progress['total_levels_completed'], 0)

    def test_get_session_progress_not_found(self):
        """Test getting progress for non-existent session."""
        with self.assertRaises(SessionNotFoundError):
            self.manager.get_session_progress("non_existent_session")

    def test_get_player_stats_success(self):
        """Test getting player stats."""
        self.manager.start_session(self.test_session_id)
        stats = self.manager.get_player_stats(self.test_session_id)
        self.assertIsInstance(stats, PlayerStats)
        self.assertEqual(stats.total_levels_completed, 0)
        self.assertEqual(stats.total_time_spent, 0.0)

    def test_get_detailed_stats_success(self):
        """Test getting detailed stats."""
        self.manager.start_session(self.test_session_id)
        detailed_stats = self.manager.get_detailed_stats(self.test_session_id)
        self.assertIn('stats', detailed_stats)
        self.assertIn('command_analysis', detailed_stats)

    @patch('banditgui.utils.progression_manager.logging')
    def test_logging(self, mock_logging):
        """Test that logging is properly configured."""
        self.manager.start_session(self.test_session_id)
        mock_logging.info.assert_called()
        mock_logging.error.assert_not_called()

if __name__ == '__main__':
    unittest.main()
