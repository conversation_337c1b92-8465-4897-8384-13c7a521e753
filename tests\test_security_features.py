import os
import time
from unittest.mock import patch

import pytest
from flask import session

from banditgui.app import app
from banditgui.utils.security_logger import SecurityLogger
from banditgui.utils.session_manager import SecureSessionManager


@pytest.fixture
def client():
    app.config['TESTING'] = True
    with app.test_client() as client:
        yield client

@pytest.fixture
def security_logger():
    return SecurityLogger(name="test_security")

@pytest.fixture
def session_manager():
    return SecureSessionManager(session_timeout=300)  # 5 minute timeout for testing

def test_rate_limiting(client):
    """Test rate limiting for API endpoints"""
    # Test normal request
    response = client.get('/progress/stats')
    assert response.status_code == 200

    # Test hitting rate limit
    for _ in range(100):  # Try to hit rate limit
        response = client.get('/progress/stats')
        assert response.status_code in [200, 429]  # Either success or rate limited

    # Test rate limit cool down
    with patch('time.time', return_value=time.time() + 60):  # Forward 60 seconds
        response = client.get('/progress/stats')
        assert response.status_code == 200  # Should work again after cooldown

def test_csrf_protection(client):
    """Test CSRF protection functionality"""
    # Create session by visiting home page
    client.get('/')
    # Test without CSRF token
    response = client.post('/progress/update', json={
        'session_id': 'test_session',
        'level': 1,
        'command': 'test',
        'output': 'test'
    })
    assert response.status_code == 400
    
    # Get CSRF token from session
    with client.session_transaction() as sess:
        csrf_token = sess.get('_csrf_token')
    
    if not csrf_token:
        # Generate token if not exists
        response = client.get('/test-csrf-token')
        csrf_token = response.get_json()['csrf_token']
    
    # Test with valid CSRF token
    headers = {'X-CSRF-Token': csrf_token}
    response = client.post('/progress/update', json={
        'session_id': 'test_session',
        'level': 1,
        'command': 'test',
        'output': 'test'
    }, headers=headers)
    assert response.status_code in (200, 400)

    # Test with invalid CSRF token
    headers = {'X-CSRF-TOKEN': 'invalid_token'}
    response = client.post('/progress/update', json={'level': 1}, headers=headers)
    assert response.status_code == 400
    assert b'CSRF token invalid' in response.data

def test_session_security(client):
    """Test session management security"""
    # Create session by visiting home page
    response = client.get('/')
    assert response.status_code == 200
    with client.session_transaction() as sess:
        sess['test_data'] = 'test_value'
    response = client.get('/')
    with client.session_transaction() as sess:
        assert sess.get('test_data') == 'test_value'

def test_api_key_security(client):
    """Test API key security features"""
    # Test without API key
    response = client.post('/ask-a-pro', json={'question': 'test'})
    assert response.status_code == 401
    
    # Test with invalid API key
    headers = {'X-API-Key': 'invalid_key'}
    response = client.post('/ask-a-pro', json={'question': 'test'}, headers=headers)
    assert response.status_code == 401

    # Test with valid API key
    with patch.dict('os.environ', {'OPENAI_API_KEY': 'test_valid_key'}):
        headers = {'X-API-Key': 'test_valid_key'}
        response = client.post('/ask-a-pro', json={'question': 'test'}, headers=headers)
        assert response.status_code == 200

def test_security_logging(client, security_logger):
    """Test security logging functionality"""
    with patch.object(security_logger, 'log_security_event') as mock_log:
        # Test authentication logging
        client.post('/ask-a-pro', json={'question': 'test'})
        mock_log.assert_called()
        
        # Test rate limit logging
        for _ in range(100):
            client.get('/progress/stats')
        mock_log.assert_called()

        # Test CSRF logging
        client.post('/progress/update', json={'level': 1})
        mock_log.assert_called()

def test_session_data_protection(client, session_manager):
    """Test protection of sensitive session data"""
    with client:
        # Create session with sensitive data
        client.get('/')
        session['sensitive_data'] = 'test_data'
        
        # Test data encryption in cookie
        cookie = client.get('/').headers.get('Set-Cookie')
        assert 'test_data' not in cookie  # Sensitive data should be encrypted
        
        # Test session invalidation on suspicious activity
        headers = {'User-Agent': 'suspicious-agent'}
        client.get('/', headers=headers)
        assert 'sensitive_data' not in session  # Session should be cleared

def test_concurrent_session_handling(client, session_manager):
    """Test handling of concurrent sessions"""
    # Create first session
    response = client.get('/')
    first_session = session['session_id']
    
    # Create second session
    client.cookie_jar.clear()  # Simulate different browser
    response = client.get('/')
    second_session = session['session_id']
    
    # Verify sessions are different
    assert first_session != second_session
    
    # Verify both sessions remain valid
    client.set_cookie('localhost', 'session_id', first_session)
    response = client.get('/progress/stats')
    assert response.status_code == 200
    
    client.set_cookie('localhost', 'session_id', second_session)
    response = client.get('/progress/stats')
    assert response.status_code == 200

def test_api_key_rotation_and_logging(security_logger):
    """Test API key automatic rotation and logging."""
    import secrets
    import time

    from banditgui.utils.api_key_manager import APIKeyManager
    
    provider = 'OPENAI'
    manager = APIKeyManager(rotation_interval=1)  # 1 second for test
    # Set initial key
    initial_key = secrets.token_hex(32)
    os.environ[f'{provider}_API_KEY'] = initial_key
    manager.keys[provider] = {
        'hashed_key': manager._hash_key(initial_key),
        'last_rotated': time.time() - 2  # Force rotation
    }
    with patch.object(security_logger, 'log_authentication') as mock_log:
        key = manager.get_key(provider)
        assert key is not None
        assert key != initial_key  # Should have rotated
        mock_log.assert_called()
        # Check that log contains key_rotation
        found = any('key_rotation' in str(call.kwargs.get('details', {})) for call in mock_log.call_args_list)
        assert found


def test_security_log_structure(security_logger):
    """Test that security logs have the expected structure."""
    from banditgui.utils.security_logger import SecurityLogger
    logger = SecurityLogger(name="test_structure")
    with patch.object(logger.logger, 'info') as mock_info:
        logger.log_security_event('test_event', {'foo': 'bar'})
        mock_info.assert_called()
        log_args = mock_info.call_args.kwargs.get('extra', {})
        details = log_args.get('details', {})
        assert 'event_type' in details
        assert 'timestamp' in details
        assert details['event_type'] == 'test_event'
        assert details['foo'] == 'bar'


def test_standardized_error_response(client):
    """Test that error responses are standardized with type and message."""
    # Trigger a known error (e.g., missing CSRF token)
    response = client.post('/progress/update', json={'level': 1})
    assert response.status_code in (400, 403)
    data = response.get_json()
    assert 'status' in data and data['status'] == 'error'
    assert 'message' in data
    assert 'error_type' in data
