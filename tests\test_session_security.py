import time
import unittest
from unittest.mock import patch

from flask import session

from banditgui.app import app as flask_app
from banditgui.utils.session_manager import SecureSessionManager


class TestSessionSecurity(unittest.TestCase):
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.app = flask_app
        self.app.config['SECRET_KEY'] = 'test_secret_key'
        self.app.config['SESSION_COOKIE_SECURE'] = True
        self.app.config['SESSION_COOKIE_HTTPONLY'] = True
        self.app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
        self.app.config['PERMANENT_SESSION_LIFETIME'] = 30 * 60  # 30 minutes
        self.app.config['SESSION_REFRESH_EACH_REQUEST'] = True
        self.app.config['TESTING'] = True
        self.app.config['SESSION_TYPE'] = 'filesystem'
        self.client = self.app.test_client()
        self.session_manager = SecureSessionManager(session_timeout=300)

    def test_session_cookie_security(self):
        """Test session cookie security flags."""
        with self.client as c:
            c.get('/')
            cookie = c.cookie_jar._cookies.get('localhost.local', {}).get('/').get('session')
            self.assertTrue(cookie.secure)
            self.assertTrue(cookie.has_nonstandard_attr('HttpOnly'))
            self.assertEqual(cookie.get_nonstandard_attr('SameSite'), 'Lax')

    def test_session_tampering_protection(self):
        """Test protection against session tampering."""
        with self.client as c:
            # Create valid session
            c.get('/')
            original_cookie = c.cookie_jar._cookies.get('localhost.local', {}).get('/').get('session').value
            
            # Attempt session tampering
            tampered_cookie = original_cookie[:-1] + ('1' if original_cookie[-1] == '0' else '0')
            c.set_cookie('localhost.local', 'session', tampered_cookie)
            
            # Session should be invalidated
            c.get('/')
            self.assertNotEqual(
                c.cookie_jar._cookies.get('localhost.local', {}).get('/').get('session').value,
                tampered_cookie
            )

    def test_session_fixation_protection(self):
        """Test protection against session fixation attacks."""
        with self.client as c:
            # Attempt to set a predefined session ID
            c.set_cookie('localhost.local', 'session_id', 'predefined_session')
            c.get('/')
            
            # Verify session ID was regenerated
            self.assertNotIn('predefined_session', str(c.cookie_jar))

    def test_session_replay_protection(self):
        """Test protection against session replay attacks."""
        with self.client as c:
            # Create and store original session
            c.get('/')
            original_cookie = c.cookie_jar._cookies.get('localhost.local', {}).get('/').get('session').value
            
            # Simulate successful login
            with c.session_transaction() as sess:
                sess['authenticated'] = True
            
            # Log out
            c.get('/logout')
            
            # Attempt to replay old session cookie
            c.set_cookie('localhost.local', 'session', original_cookie)
            c.get('/protected')
            self.assertEqual(c.status_code, 401)  # Should be unauthorized

    def test_session_timeout_behavior(self):
        """Test session timeout and renewal behavior."""
        with self.client as c:
            # Create initial session
            c.get('/')
            original_session_id = session.get('session_id')
            
            # Test session remains valid within timeout
            with patch('time.time', return_value=time.time() + 299):  # Just before timeout
                c.get('/')
                self.assertEqual(session.get('session_id'), original_session_id)
            
            # Test session invalidation after timeout
            with patch('time.time', return_value=time.time() + 301):  # Just after timeout
                c.get('/')
                self.assertNotEqual(session.get('session_id'), original_session_id)

    def test_concurrent_session_limits(self):
        """Test handling of concurrent session limits."""
        max_sessions = 3
        active_sessions = []
        
        # Create maximum allowed sessions
        for i in range(max_sessions):
            with self.client as c:
                c.get('/')
                active_sessions.append(
                    c.cookie_jar._cookies.get('localhost.local', {}).get('/').get('session').value
                )
        
        # Attempt to create one more session
        with self.client as c:
            c.get('/')
            newest_session = c.cookie_jar._cookies.get('localhost.local', {}).get('/').get('session').value
            
            # Verify oldest session was invalidated
            self.assertNotIn(active_sessions[0], str(c.cookie_jar))
            self.assertIn(newest_session, str(c.cookie_jar))

    def test_session_attribute_security(self):
        """Test security of session attributes."""
        with self.client as c:
            with c.session_transaction() as sess:
                sess['sensitive_data'] = 'test123'
                sess['user_role'] = 'admin'
            
            # Attempt to modify session attributes directly
            cookie = c.cookie_jar._cookies.get('localhost.local', {}).get('/').get('session').value
            c.set_cookie('localhost.local', 'session', cookie + 'modified')
            
            # Verify session was invalidated
            c.get('/')
            with c.session_transaction() as sess:
                self.assertNotIn('sensitive_data', sess)
                self.assertNotIn('user_role', sess)

    def test_cross_site_request_protection(self):
        """Test protection against cross-site requests."""
        with self.client as c:
            # Test with missing origin
            response = c.get('/', headers={'X-Requested-With': 'XMLHttpRequest'})
            self.assertEqual(response.status_code, 403)
            
            # Test with mismatched origin
            response = c.get('/', headers={
                'Origin': 'https://malicious-site.com',
                'X-Requested-With': 'XMLHttpRequest'
            })
            self.assertEqual(response.status_code, 403)
            
            # Test with matching origin
            response = c.get('/', headers={
                'Origin': 'https://localhost',
                'X-Requested-With': 'XMLHttpRequest'
            })
            self.assertEqual(response.status_code, 200)

    def test_secure_session_cleanup(self):
        """Test secure session cleanup on logout."""
        with self.client as c:
            # Create session with sensitive data
            with c.session_transaction() as sess:
                sess['sensitive_data'] = 'test123'
                sess['user_role'] = 'admin'
                sess['csrf_token'] = 'test_token'
            
            # Perform logout
            c.get('/logout')
            
            # Verify all sensitive data was cleared
            with c.session_transaction() as sess:
                self.assertNotIn('sensitive_data', sess)
                self.assertNotIn('user_role', sess)
                self.assertNotIn('csrf_token', sess)
                
            # Verify session cookie was properly invalidated
            cookie = c.cookie_jar._cookies.get('localhost.local', {}).get('/').get('session')
            self.assertIsNone(cookie)

if __name__ == '__main__':
    unittest.main()
